2025-08-03 12:08:28,437 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:09:14,674 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:09:23,686 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:09:24,037 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:09:24,556 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:09:24,600 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:09:26,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:09:27,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:09:28,005 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:09:32,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:09:32,840 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:09:35,674 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:09:37,137 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c "HTTP/1.1 200 OK"
2025-08-03 12:09:40,277 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/navigate "HTTP/1.1 200 OK"
2025-08-03 12:09:53,749 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/act "HTTP/1.1 200 OK"
2025-08-03 12:09:59,371 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:03,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:03,479 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:07,094 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/extract "HTTP/1.1 200 OK"
2025-08-03 12:10:12,220 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:17,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:17,471 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:17,547 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:22,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:22,335 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:25,914 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/extract "HTTP/1.1 200 OK"
2025-08-03 12:10:30,164 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:37,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:37,991 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:39,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:40,572 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:48,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:48,509 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:49,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:49,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:50,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:51,638 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/end "HTTP/1.1 200 OK"
2025-08-03 12:11:52,363 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:12:37,299 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:12:37,608 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:12:38,060 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:12:38,102 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:12:38,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:12:39,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:12:39,601 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:12:44,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:12:44,265 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:12:47,223 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:12:48,687 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b "HTTP/1.1 200 OK"
2025-08-03 12:12:53,744 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/navigate "HTTP/1.1 200 OK"
2025-08-03 12:13:13,838 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/act "HTTP/1.1 200 OK"
2025-08-03 12:13:23,053 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:13:26,859 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:13:26,869 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:13:35,414 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/extract "HTTP/1.1 200 OK"
2025-08-03 12:14:06,461 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:14:21,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:14:21,552 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:14:21,609 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:14:55,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:14:56,004 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:14:56,122 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:09,928 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:09,932 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:09,973 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:26,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:26,287 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:26,302 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:43,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:43,104 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:43,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:44,771 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:53,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:53,658 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:54,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:55,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:56,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:57,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:57,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:58,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:16:00,879 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/end "HTTP/1.1 200 OK"
2025-08-03 12:24:00,902 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:24:05,618 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:24:15,003 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:24:15,895 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:24:16,248 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:24:16,739 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:24:16,784 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:24:18,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:24:19,417 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:24:19,543 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:23,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:23,963 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:26,829 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:24:28,287 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc "HTTP/1.1 200 OK"
2025-08-03 12:24:31,394 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/navigate "HTTP/1.1 200 OK"
2025-08-03 12:24:37,064 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:40,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:40,073 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:42,087 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information...
2025-08-03 12:24:43,885 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/extract "HTTP/1.1 200 OK"
2025-08-03 12:24:49,238 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:24:49,265 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:53,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:53,020 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:55,032 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Identify and analyze elements on the page to ensur...
2025-08-03 12:24:56,495 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/observe "HTTP/1.1 200 OK"
2025-08-03 12:25:10,792 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:25:10,834 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:14,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:14,920 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:16,933 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product title, description, prices, and ot...
2025-08-03 12:25:18,434 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/extract "HTTP/1.1 200 OK"
2025-08-03 12:25:21,879 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:25:21,907 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:27,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:27,474 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:28,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:28,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:28,694 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:33,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:33,472 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:40,485 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Check if the page is accessible and properly loade...
2025-08-03 12:25:42,029 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/observe "HTTP/1.1 200 OK"
2025-08-03 12:25:46,527 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:25:46,562 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:50,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:50,638 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:52,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:53,303 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:58,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:58,764 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:59,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:00,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:00,098 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:05,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:05,277 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:05,295 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:07,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:07,923 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:08,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:08,555 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:12,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:12,219 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:12,329 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:26:13,668 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/end "HTTP/1.1 200 OK"
2025-08-03 12:26:13,738 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:26:13,738 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:26:33,399 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:26:38,523 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:26:48,450 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:26:49,425 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:26:49,955 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:26:50,486 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:26:50,545 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:26:51,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:52,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:52,972 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:58,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:58,356 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:01,261 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:27:02,700 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0 "HTTP/1.1 200 OK"
2025-08-03 12:27:06,401 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/navigate "HTTP/1.1 200 OK"
2025-08-03 12:27:12,413 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:17,299 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:17,301 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:24,314 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information includin...
2025-08-03 12:27:26,143 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/extract "HTTP/1.1 200 OK"
2025-08-03 12:27:37,287 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:27:37,308 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:47,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:47,364 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:47,411 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:55,568 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:55,619 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:56,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:27:57,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:27:57,902 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:02,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:02,377 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:03,653 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/navigate "HTTP/1.1 200 OK"
2025-08-03 12:28:08,482 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:24,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:24,426 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:25,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:27,080 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:37,168 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:37,176 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:38,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:39,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:39,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:40,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:41,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:41,960 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:42,127 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:46,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:46,414 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:53,440 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Inspect the page source for structured data or hid...
2025-08-03 12:28:55,298 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/observe "HTTP/1.1 200 OK"
2025-08-03 12:28:57,288 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:28:57,323 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:10,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:10,730 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:10,754 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:23,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:23,703 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:24,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:24,514 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:34,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:34,067 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:34,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:34,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:35,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:35,696 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,983 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:29:38,437 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/end "HTTP/1.1 200 OK"
2025-08-03 12:29:38,499 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:29:38,499 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:30:24,998 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:30:30,935 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:30:40,410 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:30:41,356 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:30:41,774 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:30:42,299 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:30:42,347 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:30:43,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:30:44,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:30:44,513 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:30:49,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:30:49,559 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:30:52,423 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:30:53,963 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1 "HTTP/1.1 200 OK"
2025-08-03 12:31:00,808 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/navigate "HTTP/1.1 200 OK"
2025-08-03 12:31:06,857 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:10,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:10,648 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:12,659 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all product information including title, d...
2025-08-03 12:31:15,437 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/extract "HTTP/1.1 200 OK"
2025-08-03 12:31:21,709 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:31:21,730 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:27,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:27,931 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:27,984 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:39,151 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:39,165 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:40,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:41,320 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:47,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:47,411 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:48,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:49,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:49,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:50,003 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:31:51,410 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/end "HTTP/1.1 200 OK"
2025-08-03 12:31:51,583 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:31:51,584 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:33:45,320 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:33:50,874 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:33:59,607 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:00,467 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:00,854 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:34:01,451 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:01,492 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:34:01,500 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 12:34:02,546 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 12:34:18,632 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:23,962 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:34:33,254 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:34,155 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:34,536 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:34:35,049 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:35,093 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:34:35,845 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:34:37,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:34:37,309 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:34:41,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:34:41,390 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:34:44,186 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:34:45,730 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d "HTTP/1.1 200 OK"
2025-08-03 12:34:48,875 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/navigate "HTTP/1.1 200 OK"
2025-08-03 12:34:55,117 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:34:58,952 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:34:58,961 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:00,973 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information includin...
2025-08-03 12:35:03,579 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/extract "HTTP/1.1 200 OK"
2025-08-03 12:35:08,931 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:35:08,959 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:13,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:13,430 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:13,495 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:20,356 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:20,368 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:21,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:22,035 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:30,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:30,930 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:31,560 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:31,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:32,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:32,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:33,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:33,250 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:35:34,621 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/end "HTTP/1.1 200 OK"
2025-08-03 12:35:34,706 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:35:34,707 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:40:34,397 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:40:39,454 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:40:48,666 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:40:58,827 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:41:04,279 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:41:13,963 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:41:14,894 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:41:15,289 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:41:15,786 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:41:15,839 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:41:17,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:41:18,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:41:18,312 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:41:23,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:41:23,163 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:41:25,922 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:41:27,451 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/51d6a394-289d-4f74-bc3b-8405ef677e50 "HTTP/1.1 200 OK"
2025-08-03 12:41:30,721 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/51d6a394-289d-4f74-bc3b-8405ef677e50/navigate "HTTP/1.1 200 OK"
2025-08-03 12:41:37,019 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:41:40,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:41:40,884 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:41:42,893 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information includin...
2025-08-03 12:41:44,670 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/51d6a394-289d-4f74-bc3b-8405ef677e50/extract "HTTP/1.1 200 OK"
2025-08-03 12:41:54,553 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:41:54,574 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:41:58,760 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:41:58,765 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:41:58,816 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:42:05,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:42:05,299 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:42:06,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:42:07,236 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:42:15,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:42:15,354 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:42:15,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:42:19,086 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:42:19,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:42:20,019 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:42:21,395 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/51d6a394-289d-4f74-bc3b-8405ef677e50/end "HTTP/1.1 200 OK"
2025-08-03 12:42:21,477 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:42:21,478 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 13:48:07,550 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 13:48:12,021 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 13:48:19,748 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 13:49:07,431 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 13:49:11,992 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:05:33,583 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:05:38,728 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:05:48,572 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_140548_c237aabd
2025-08-03 14:05:48,577 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_140548_c237aabd - test_vendor/test_category
2025-08-03 14:12:02,921 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:12:07,551 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:12:15,581 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_141215_b50fc341
2025-08-03 14:12:15,583 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_141215_b50fc341 - test_vendor/test_category
2025-08-03 14:12:51,110 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:12:55,480 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:13:58,155 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:14:03,300 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:16:57,966 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:17:02,710 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:17:10,715 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_141710_efb5b7db
2025-08-03 14:17:10,726 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_141710_34258faf
2025-08-03 14:17:10,730 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_141710_ec048420
2025-08-03 14:20:00,724 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:20:06,150 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:20:15,603 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_9fb59a47
2025-08-03 14:20:15,604 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_89821708
2025-08-03 14:20:15,604 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_test_category_1754227215 - test_vendor/test_category
2025-08-03 14:20:15,613 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_c75acef1
2025-08-03 14:20:15,618 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_cfe6989e
2025-08-03 14:20:15,618 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_8abeff79
2025-08-03 14:20:15,619 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_0_1754227215 - test_vendor/category_0
2025-08-03 14:20:15,619 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_db0af18b
2025-08-03 14:20:15,620 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_1_1754227215 - test_vendor/category_1
2025-08-03 14:20:15,620 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142015_95bf2052
2025-08-03 14:20:15,621 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_2_1754227215 - test_vendor/category_2
2025-08-03 14:23:07,633 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:23:13,800 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:23:25,547 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_c32e9179
2025-08-03 14:23:25,566 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_b0cc8488
2025-08-03 14:23:25,566 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_test_category_1754227405 - test_vendor/test_category
2025-08-03 14:23:25,567 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_694661a2
2025-08-03 14:23:25,568 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_2_test_category_2_1754227405 - test_vendor_2/test_category_2
2025-08-03 14:23:25,598 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_13ee0db2
2025-08-03 14:23:25,600 - ecommerce_scraper.state.state_manager - INFO - Resumed session: scraping_20250803_142325_13ee0db2
2025-08-03 14:23:25,667 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_0901903a
2025-08-03 14:23:25,669 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_8de9e256
2025-08-03 14:23:25,669 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_0_1754227405 - test_vendor/category_0
2025-08-03 14:23:25,671 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_9008135a
2025-08-03 14:23:25,672 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_1_1754227405 - test_vendor/category_1
2025-08-03 14:23:25,673 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_142325_8b2dfa93
2025-08-03 14:23:25,674 - ecommerce_scraper.batch.batch_processor - INFO - Added job: test_vendor_category_2_1754227405 - test_vendor/category_2
2025-08-03 14:36:11,369 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:36:17,263 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:37:29,556 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:37:35,486 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:38:11,349 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:38:16,591 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:39:46,200 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:39:51,550 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:40:53,472 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:40:58,830 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:41:08,546 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_144108_280f663b
2025-08-03 14:41:34,459 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:41:40,507 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:41:50,137 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_144150_83fb18ff
2025-08-03 14:43:05,208 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 14:43:11,393 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 14:43:21,913 - ecommerce_scraper.state.state_manager - INFO - Created new session: scraping_20250803_144321_057d752d
2025-08-03 15:03:32,740 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:03:37,676 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 15:04:28,700 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:04:32,713 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 15:14:16,193 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:14:20,471 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 15:14:39,868 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:14:44,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:14:44,803 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:14:47,367 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 15:14:48,715 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452 "HTTP/1.1 200 OK"
2025-08-03 15:14:52,086 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452/navigate "HTTP/1.1 200 OK"
2025-08-03 15:15:05,929 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:15:10,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:15:10,085 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:15:15,089 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Analyze the main navigation structure for product ...
2025-08-03 15:15:16,912 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452/observe "HTTP/1.1 200 OK"
2025-08-03 15:15:21,025 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 15:15:21,046 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:15:25,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:15:25,476 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:15:29,480 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract category names, URLs, and product counts f...
2025-08-03 15:15:31,090 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452/extract "HTTP/1.1 200 OK"
2025-08-03 15:15:31,902 - ecommerce_scraper.tools.stagehand_tool - WARNING - Attempt 1 failed: cannot reuse already awaited coroutine
2025-08-03 15:15:32,903 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 2): Extract category names, URLs, and product counts f...
2025-08-03 15:19:56,276 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/6db1eeca-1792-468a-b174-fbfd8ce6e452/end "HTTP/1.1 410 Gone"
2025-08-03 15:20:30,916 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:20:36,117 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 15:20:59,642 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:04,488 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:04,504 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:21:04,511 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 15:21:04,527 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:08,087 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:08,089 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:21:08,106 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:11,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:11,618 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:21:11,621 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 15:21:11,653 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:26,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:26,660 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:21:26,662 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 15:21:37,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 15:21:37,542 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 15:48:39,669 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:49:11,624 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:50:05,179 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:56:24,051 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:57:43,643 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 15:57:54,790 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:00:16,907 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:00:22,033 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:05:16,026 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:05:22,441 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:22:23,035 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:22:28,359 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:27:02,780 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:27:09,991 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:46:02,175 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:46:06,872 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:48:51,563 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:48:55,991 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:56:20,377 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:56:26,322 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:57:02,008 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:57:06,977 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 16:57:31,804 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 16:57:36,648 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 18:57:46,055 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 18:57:53,795 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:00:45,369 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:00:50,207 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:01:23,703 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:01:28,015 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:02:13,659 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:02:18,319 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:15:05,754 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:15:11,340 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:16:38,581 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:16:43,275 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:19:38,666 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:19:45,046 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:21:39,751 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:22:31,407 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:22:37,409 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:22:54,523 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:22:59,578 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:35:38,470 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:35:42,821 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:36:07,680 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:36:12,218 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:36:29,199 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:36:33,916 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:37:38,590 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:37:43,075 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:37:50,972 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754246270 for asda/Rollback
2025-08-03 19:37:50,973 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_>_Laundry,_Household_&_Toiletries_1754246270 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:38:21,093 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:38:25,460 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:38:34,616 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754246314 for asda/Rollback
2025-08-03 19:38:34,616 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754246314 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:40:17,588 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:40:22,110 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:40:30,267 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754246430 for asda/Rollback
2025-08-03 19:40:30,268 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754246430 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:41:44,837 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:41:49,262 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:41:57,177 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754246517 for asda/Rollback
2025-08-03 19:52:15,268 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:52:19,839 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:53:42,148 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247222 for asda/Rollback
2025-08-03 19:53:42,148 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754247222 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:53:42,152 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 19:53:42,152 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754247222
2025-08-03 19:53:42,153 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 19:53:42,153 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 19:53:42,153 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 19:53:42,153 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback___Laundry_Household_and_Toiletries_1754247222
2025-08-03 19:53:42,156 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:53:42,156 - ecommerce_scraper.state.state_manager - ERROR - Failed to save state: [Errno 22] Invalid argument: 'scraping_state\\scraping_20250803_195303_asda_Rollback > Laundry, Household & Toiletries.json'
2025-08-03 19:53:42,159 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:53:42,161 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_195303 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 19:53:42,163 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 19:53:42,175 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:53:42,175 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:53:42,185 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:53:42,185 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 0 error scraping URL https://groceries.asda.com/groceries/rollback/1215684421135: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 19:53:42,186 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754247222 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 19:53:42,187 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 starting URL scrape: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 19:53:42,187 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754247222 (attempt 1)
2025-08-03 19:53:42,187 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 1 error scraping URL https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 19:53:42,189 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 processing job: asda_Rollback_1754247222
2025-08-03 19:53:42,189 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback___Laundry_Household_and_Toiletries_1754247222 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 19:53:42,189 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 stopped
2025-08-03 19:53:42,189 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:53:42,190 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback___Laundry_Household_and_Toiletries_1754247222 (attempt 1)
2025-08-03 19:53:42,192 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 19:53:42,192 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:53:42,198 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:53:42,198 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 2 error scraping URL https://groceries.asda.com/groceries/rollback/1215684421135: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 19:53:42,199 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754247222 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 19:53:42,199 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754247222 (attempt 2)
2025-08-03 19:53:42,201 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 19:53:42,202 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 19:53:42,202 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 19:58:33,615 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:58:40,220 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:58:51,675 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:58:51,675 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247531 for asda/Rollback
2025-08-03 19:59:36,692 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 19:59:41,696 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 19:59:50,658 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:59:50,670 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247590 for asda/Rollback
2025-08-03 19:59:50,673 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 19:59:50,674 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754247590
2025-08-03 19:59:50,674 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 19:59:50,674 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 19:59:50,674 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 19:59:50,676 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_195950 - asda/Rollback
2025-08-03 19:59:50,678 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:59:50,688 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 19:59:50,720 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 19:59:50,726 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 19:59:53,360 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 19:59:54,790 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 19:59:55,366 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 19:59:55,915 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 19:59:57,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 19:59:57,287 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 19:59:59,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 19:59:59,056 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 19:59:59,091 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:03,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:03,657 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:03,672 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:00:03,688 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:07,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:07,752 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:07,768 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:19,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:19,132 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:20,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:00:20,714 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:00:21,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:00:21,384 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:00:21,393 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:25,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:25,347 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:25,351 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:00:25,365 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:28,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:28,981 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:29,000 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:33,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:34,184 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:34,187 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:00:34,210 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:39,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:39,294 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:39,296 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:49,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:00:49,326 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:00:50,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:00:50,626 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:00:52,314 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:00:55,130 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:00:55,130 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:00:55,147 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247655 for asda/Rollback
2025-08-03 20:00:55,148 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_>_Laundry,_Household_&_Toiletries_1754247655 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:00:55,154 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:00:55,155 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754247655
2025-08-03 20:00:55,155 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:00:55,156 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback_>_Laundry,_Household_&_Toiletries_1754247655
2025-08-03 20:00:55,156 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:00:55,156 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:00:55,157 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:00:55,158 - ecommerce_scraper.state.state_manager - ERROR - Failed to save state: [Errno 22] Invalid argument: 'scraping_state\\scraping_20250803_200053_asda_Rollback > Laundry, Household & Toiletries.json'
2025-08-03 20:00:55,161 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:00:55,162 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200053 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:00:55,167 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:00:55,170 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:00:55,172 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:00:55,173 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:00:55,174 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:00:55,196 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:00:55,196 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:00:55,218 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:00:55,219 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 starting URL scrape: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:00:55,221 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 0 error scraping URL https://groceries.asda.com/groceries/rollback/1215684421135: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 20:00:55,223 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 1 error scraping URL https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 20:00:55,223 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754247655 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 20:00:55,224 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_>_Laundry,_Household_&_Toiletries_1754247655 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 20:00:55,225 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754247655 (attempt 1)
2025-08-03 20:00:55,225 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_>_Laundry,_Household_&_Toiletries_1754247655 (attempt 1)
2025-08-03 20:00:55,228 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 processing job: asda_Rollback_1754247655
2025-08-03 20:00:55,229 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 stopped
2025-08-03 20:00:55,230 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:00:55,230 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:00:55,232 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:00:55,233 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:00:55,236 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:00:55,249 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:00:55,249 - ecommerce_scraper.batch.batch_processor - ERROR - Worker 2 error scraping URL https://groceries.asda.com/groceries/rollback/1215684421135: 'EcommerceScraper' object has no attribute 'scrape_url'
2025-08-03 20:00:55,250 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754247655 failed: 'PaginationState' object has no attribute 'mark_complete'
2025-08-03 20:00:55,250 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754247655 (attempt 2)
2025-08-03 20:00:55,251 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:00:55,253 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:00:55,254 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:01:05,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:05,949 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:06,811 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:06,816 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:07,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:08,061 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:08,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:08,581 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:09,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:10,898 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:11,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:13,178 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:14,370 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:14,401 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:14,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:15,385 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:15,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:15,846 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:16,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:16,351 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:17,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:18,177 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:18,686 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:18,799 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:18,824 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:24,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:24,640 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:24,644 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:01:24,662 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:25,812 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:01:25,813 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:01:30,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:30,868 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:31,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:31,928 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:01:33,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:01:33,782 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:01:33,791 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:37,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:37,754 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:37,758 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:01:37,776 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:42,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:42,039 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:42,056 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:45,973 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:46,094 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:46,098 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:01:46,120 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:49,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:49,358 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:01:49,362 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:01:49,376 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:01:52,853 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:01:52,863 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:01:59,394 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:01:59,562 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:03:21,635 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:03:26,160 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:03:57,814 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:03:57,814 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:03:57,815 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:03:57,826 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754247837 for asda/Rollback
2025-08-03 20:03:57,827 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754247837 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:03:57,831 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:03:57,832 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754247837
2025-08-03 20:03:57,832 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:03:57,832 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:03:57,833 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:03:57,833 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback___Laundry_Household_and_Toiletries_1754247837
2025-08-03 20:03:57,837 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:03:57,839 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:03:57,840 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:03:57,841 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:03:57,842 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:03:57,843 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:03:57,843 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:03:57,844 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:03:57,845 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:03:57,847 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:03:57,860 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:03:57,861 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:03:57,876 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:03:57,876 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:03:57,892 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:03:57,892 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 starting URL scrape: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:03:58,797 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:03:59,094 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:03:59,326 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:03:59,677 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:03:59,727 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:03:59,960 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:04:00,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:00,673 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:00,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:00,938 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:01,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:01,349 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:01,374 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:02,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:02,535 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:02,542 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:06,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:06,365 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:06,372 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:06,383 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:06,585 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:06,590 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:06,595 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:06,605 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:09,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:09,676 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:09,680 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:09,689 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:11,485 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:11,486 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:12,190 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:12,194 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:13,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:13,868 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:13,874 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:16,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:16,568 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:17,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:17,827 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:18,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:18,200 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:18,210 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:18,219 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:19,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:19,317 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:19,322 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:21,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:21,619 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:21,622 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:21,632 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:21,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:21,833 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:21,837 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:21,849 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:22,858 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:04:24,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:24,290 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:24,294 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:24,305 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:24,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:24,966 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:24,972 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:24,984 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:27,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:27,015 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:27,018 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:27,029 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:28,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:28,242 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:28,255 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:29,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:29,682 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:29,686 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:29,698 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:31,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:31,362 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:31,365 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:35,238 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:35,242 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:35,244 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:35,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:35,844 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:36,931 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:36,950 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:37,619 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:38,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:38,188 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:39,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:39,672 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:39,677 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:42,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:42,660 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:42,694 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:43,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:43,418 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:44,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:44,013 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:44,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:44,426 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:44,786 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:44,790 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:45,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:45,452 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:45,463 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:48,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:48,910 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:49,898 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:49,901 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:50,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:50,076 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:50,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:50,641 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:04:50,648 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:50,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:04:50,758 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:04:50,764 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:04:53,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:53,233 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:53,262 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:04:53,262 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraped 0 products from https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:04:53,267 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback___Laundry_Household_and_Toiletries_1754247837 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:04:53,269 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback___Laundry_Household_and_Toiletries_1754247837 (attempt 1)
2025-08-03 20:04:53,270 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:04:57,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:04:57,285 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:04:57,290 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:04:57,301 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:05:10,409 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:05:10,417 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:05:10,974 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:05:10,979 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:05:10,985 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:05:18,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:05:18,988 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:05:18,996 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:07:40,815 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:07:45,868 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:08:00,749 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:08:00,750 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:08:00,750 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:08:00,751 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:08:00,752 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:08:00,761 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754248080 for asda/Rollback
2025-08-03 20:08:00,765 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:08:00,765 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754248080
2025-08-03 20:08:00,766 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:08:00,766 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:08:00,767 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:08:00,768 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:08:00,770 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:08:00,774 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:08:00,775 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:08:00,775 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:08:00,776 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:08:00,776 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:08:00,793 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:08:00,808 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:08:01,710 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:08:02,080 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:08:02,641 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:08:02,690 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:08:05,945 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:05,974 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:08:13,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:13,574 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:08:13,599 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:19,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:19,120 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:19,126 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:19,143 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:24,314 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:24,316 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:24,320 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:24,337 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:29,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:29,863 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:31,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:31,533 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:08:33,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:33,204 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:08:33,211 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:38,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:38,039 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:38,043 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:38,057 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:41,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:41,064 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:41,068 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:41,084 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:44,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:44,712 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:44,715 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:08:44,736 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:47,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:47,380 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:47,400 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:50,903 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:50,905 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:50,908 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:53,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:53,756 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:55,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:08:55,190 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:08:55,902 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:08:59,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:08:59,295 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:08:59,330 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:09:03,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:09:03,497 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:09:03,515 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:09:07,729 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:09:07,738 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:09:08,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:09:08,646 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:09:10,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:09:10,402 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:09:10,410 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:09:15,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:09:30,919 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:09:30,919 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:09:57,374 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:09:57,378 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:09:57,392 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:09:58,996 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:09:58,997 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:10:24,293 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:10:29,507 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:12:48,238 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:12:48,238 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:12:48,239 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:12:48,240 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:12:48,240 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,241 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:12:48,255 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754248368 for asda/Rollback
2025-08-03 20:12:48,256 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754248368 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:12:48,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754248368
2025-08-03 20:12:48,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:12:48,261 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:12:48,261 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback___Laundry_Household_and_Toiletries_1754248368
2025-08-03 20:12:48,261 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:12:48,263 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:12:48,265 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:12:48,268 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,268 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:12:48,269 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:12:48,271 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:12:48,272 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:12:48,272 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:12:48,274 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:12:48,274 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:12:48,274 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,275 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:12:48,275 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:12:48,276 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:12:48,276 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,278 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:12:48,290 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:12:48,290 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:12:48,303 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,303 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:12:48,317 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 starting URL scrape: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:12:48,321 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 starting URL scrape: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:12:49,236 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:12:49,639 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:12:49,764 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:12:50,214 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:12:50,255 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:12:50,374 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:12:51,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:12:52,004 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:12:52,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:12:52,674 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:12:59,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:12:59,228 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:12:59,261 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:03,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:03,672 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:03,683 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:03,697 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:04,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:04,072 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:04,081 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:07,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:07,755 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:07,760 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:07,768 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:08,730 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:08,738 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:08,743 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:08,753 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:12,781 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:12,783 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:12,786 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:12,795 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:13,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:13,494 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:15,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:15,031 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:17,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:17,377 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:17,388 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:18,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:18,481 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:20,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:20,571 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:20,579 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:20,602 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:23,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:23,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:23,790 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:23,802 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:23,825 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:24,387 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:25,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:26,010 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:26,011 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:26,014 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:26,025 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:28,090 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:28,096 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:28,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:28,795 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:28,798 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:28,809 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:32,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:32,669 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:32,673 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:32,684 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:32,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:32,960 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:32,962 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:35,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:35,255 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:35,258 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:35,269 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:35,307 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:35,316 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:36,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:36,784 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:37,446 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:38,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:38,663 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:38,666 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:38,679 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:41,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:41,092 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:41,094 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:42,861 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:42,865 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:43,359 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:13:43,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:43,580 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:43,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:43,823 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:43,830 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:44,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:44,468 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:45,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:45,034 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:45,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:45,614 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:45,635 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:48,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:48,867 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:49,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:49,928 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:50,381 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:50,471 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:50,493 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:52,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:52,742 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:53,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:53,598 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:54,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:54,296 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:54,305 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:55,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:55,158 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:55,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:55,869 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:13:56,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:13:56,665 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:13:56,674 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:13:59,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:13:59,499 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:13:59,504 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:13:59,521 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:01,307 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:01,313 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:01,316 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:14:01,333 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:05,136 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:05,165 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:05,172 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:14:05,189 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:09,739 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:09,761 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:09,764 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:14:09,788 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:13,454 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:13,456 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:13,459 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:13,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:13,996 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:14,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:14,659 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:14:14,663 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:19,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:19,816 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:20,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:20,556 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:14:20,561 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:20,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:20,677 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:21,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:21,948 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:22,678 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:22,684 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:23,167 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:23,171 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:23,185 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:27,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:27,231 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:27,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:27,863 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:27,867 - ecommerce_scraper.tools.stagehand_tool - WARNING - Threading signal error (non-critical): signal only works in main thread of the main interpreter
2025-08-03 20:14:27,883 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:28,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:28,036 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:28,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:28,345 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:28,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:28,665 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:29,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:29,056 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:29,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:29,376 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:14:29,388 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:32,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:32,429 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:32,432 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:33,232 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:33,237 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:34,529 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:34,534 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:14:34,538 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:14:38,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:38,201 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:38,992 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:14:39,000 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:14:39,033 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:14:39,033 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraped 0 products from https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:14:39,034 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback___Laundry_Household_and_Toiletries_1754248368 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:14:39,035 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback___Laundry_Household_and_Toiletries_1754248368 (attempt 1)
2025-08-03 20:14:39,036 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:14:39,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:14:39,083 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:14:39,088 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:28:40,780 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:28:45,224 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:28:53,171 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:28:53,172 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:28:53,173 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:28:53,174 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:28:53,175 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:28:53,176 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:28:53,177 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:28:53,178 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:28:53,191 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754249333 for asda/Rollback
2025-08-03 20:28:53,194 - ecommerce_scraper.batch.batch_processor - INFO - Stagehand processor started in main thread
2025-08-03 20:28:53,194 - ecommerce_scraper.batch.batch_processor - INFO - Started Stagehand processor thread
2025-08-03 20:28:53,195 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:28:53,196 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754249333
2025-08-03 20:28:53,196 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:28:53,196 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:28:53,196 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:28:53,197 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:28:53,199 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:28:53,209 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 delegating Stagehand operation to main thread for: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:28:53,210 - ecommerce_scraper.batch.batch_processor - INFO - Main thread processing Stagehand operation scrape_-1521094302663175139_0 for worker 0
2025-08-03 20:28:53,211 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:28:53,212 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:28:53,213 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:28:53,214 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:28:53,215 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:28:53,216 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:28:53,216 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:28:53,217 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:28:53,229 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:28:54,025 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:28:54,320 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:28:54,776 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:28:54,811 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:28:55,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:28:55,679 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:28:56,416 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:28:56,434 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:28:56,457 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:02,534 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:02,543 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:02,549 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:29:02,563 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:09,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:09,085 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:09,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:29:09,746 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:29:11,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:29:11,247 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:29:11,254 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:16,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:16,352 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:16,355 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:29:16,369 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:21,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:21,620 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:21,634 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:26,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:26,039 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:26,042 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:29:26,063 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:39,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:39,073 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:39,075 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:29:44,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:29:44,450 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:29:56,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:29:56,093 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:29:56,807 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:02,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:02,304 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:03,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:03,556 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:04,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:04,134 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:04,148 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:09,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:09,636 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:09,656 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:14,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:14,527 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:15,179 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:15,200 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:30:15,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:15,985 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:15,992 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:22,003 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:22,013 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:22,016 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:30:22,034 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:34,847 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:34,849 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:35,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:35,701 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:30:35,706 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:43,205 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:43,209 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:43,825 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:43,836 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:44,187 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:44,196 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:44,524 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:44,528 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:44,544 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:47,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:47,379 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:48,156 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:48,168 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:30:48,173 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:30:52,683 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:30:52,695 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:30:52,726 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:30:52,726 - ecommerce_scraper.batch.batch_processor - INFO - Main thread completed operation scrape_-1521094302663175139_0, 0 products
2025-08-03 20:30:53,346 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 received result from main thread for https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:30:53,347 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754249333 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:30:53,348 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754249333 (attempt 1)
2025-08-03 20:30:53,350 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback_1754249333
2025-08-03 20:30:53,351 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:30:53,351 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 delegating Stagehand operation to main thread for: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:30:53,351 - ecommerce_scraper.batch.batch_processor - INFO - Main thread processing Stagehand operation scrape_-1521094302663175139_1 for worker 1
2025-08-03 20:30:53,353 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:30:53,354 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:30:53,354 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:30:53,355 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:30:53,357 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:30:53,358 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:30:53,359 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:30:53,360 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:30:53,363 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:30:53,992 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:30:54,537 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:30:55,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:55,291 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:30:55,916 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:30:56,067 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:30:56,083 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:01,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:01,294 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:01,298 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:31:01,314 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:05,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:05,973 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:05,989 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:13,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:13,772 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:14,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:14,605 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:31:15,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:15,428 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:15,435 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:18,400 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 stopped
2025-08-03 20:31:18,469 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:31:24,043 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:31:28,559 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:31:34,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:34,105 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:35,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:35,103 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:31:35,108 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:31:45,916 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:31:45,919 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:31:46,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:46,711 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:47,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:47,384 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:48,769 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:48,773 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:49,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:49,118 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:49,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:49,439 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:49,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:49,951 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:50,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:50,741 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:51,065 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:51,091 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:51,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:51,658 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:53,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:31:53,183 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:31:53,480 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:00,184 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:00,187 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:01,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:01,761 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:32:01,767 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:05,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:05,371 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:05,403 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:32:05,403 - ecommerce_scraper.batch.batch_processor - INFO - Main thread completed operation scrape_-1521094302663175139_1, 0 products
2025-08-03 20:32:05,404 - ecommerce_scraper.batch.batch_processor - INFO - Stagehand processor stopped
2025-08-03 20:32:05,404 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:32:05,405 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:32:05,510 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 received result from main thread for https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:32:05,511 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754249333 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:32:05,512 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754249333 (attempt 2)
2025-08-03 20:32:05,516 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 stopped
2025-08-03 20:32:16,222 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:32:16,223 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:32:16,224 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:32:16,224 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:32:16,225 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,226 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:32:16,229 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:32:16,232 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,233 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:32:16,251 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback_1754249536 for asda/Rollback
2025-08-03 20:32:16,252 - ecommerce_scraper.batch.batch_processor - INFO - Added URL-based job: asda_Rollback___Laundry_Household_and_Toiletries_1754249536 for asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,258 - ecommerce_scraper.batch.batch_processor - INFO - Stagehand processor started in main thread
2025-08-03 20:32:16,258 - ecommerce_scraper.batch.batch_processor - INFO - Started Stagehand processor thread
2025-08-03 20:32:16,259 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 started
2025-08-03 20:32:16,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 processing job: asda_Rollback_1754249536
2025-08-03 20:32:16,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 started
2025-08-03 20:32:16,260 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 processing job: asda_Rollback___Laundry_Household_and_Toiletries_1754249536
2025-08-03 20:32:16,261 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 started
2025-08-03 20:32:16,261 - ecommerce_scraper.batch.batch_processor - INFO - Started 3 batch processing workers
2025-08-03 20:32:16,263 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_203214 - asda/Rollback
2025-08-03 20:32:16,266 - ecommerce_scraper.state.state_manager - INFO - Created pagination state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,271 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 scraping URL: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:32:16,273 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 scraping URL: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:32:16,274 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 delegating Stagehand operation to main thread for: https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:32:16,274 - ecommerce_scraper.batch.batch_processor - INFO - Worker 1 delegating Stagehand operation to main thread for: https://groceries.asda.com/dept/rollback/laundry-household-toiletries/1215684421135-1215686356584
2025-08-03 20:32:16,275 - ecommerce_scraper.batch.batch_processor - INFO - Main thread processing Stagehand operation scrape_8249459401766103418_0 for worker 0
2025-08-03 20:32:16,279 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:32:16,280 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:32:16,282 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:32:16,283 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:32:16,285 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,287 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:32:16,287 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:32:16,288 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:16,289 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:32:16,307 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 20:32:16,324 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:32:17,149 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:32:17,470 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:32:18,035 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:32:18,091 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:32:19,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:19,817 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:32:20,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:20,349 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:32:20,372 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:25,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:25,248 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:25,256 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:32:25,275 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:32,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:32,444 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:33,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:33,365 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:32:34,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:34,866 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:32:34,873 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:41,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:41,140 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:41,144 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:32:41,162 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:32:55,374 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:32:55,381 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:32:56,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:32:56,306 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:32:57,667 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:05,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:05,198 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:06,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:06,610 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:07,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:07,117 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:07,152 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:14,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:14,117 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:14,120 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:33:14,142 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:22,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:22,012 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:22,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:22,788 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:33:23,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:23,483 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:23,493 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:27,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:27,813 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:27,832 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:30,844 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:30,848 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:30,864 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:38,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:38,535 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:39,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:39,689 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:33:39,693 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:47,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:47,739 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:48,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:48,568 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:49,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:49,107 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:50,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:50,021 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:33:50,038 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:33:54,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:33:54,265 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:33:56,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:33:56,137 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:33:56,142 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:34:01,410 - ecommerce_scraper.batch.batch_processor - INFO - Worker 2 stopped
2025-08-03 20:34:03,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:34:03,726 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:34:03,751 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:34:03,751 - ecommerce_scraper.batch.batch_processor - INFO - Main thread completed operation scrape_8249459401766103418_0, 0 products
2025-08-03 20:34:03,752 - ecommerce_scraper.batch.batch_processor - INFO - Stagehand processor stopped
2025-08-03 20:34:04,340 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 received result from main thread for https://groceries.asda.com/groceries/rollback/1215684421135
2025-08-03 20:34:04,340 - ecommerce_scraper.batch.batch_processor - ERROR - Job asda_Rollback_1754249536 failed: 1 validation error for ProductBatch
products
  Value error, Products list cannot be empty [type=value_error, input_value=[], input_type=list]
    For further information visit https://errors.pydantic.dev/2.11/v/value_error
2025-08-03 20:34:04,341 - ecommerce_scraper.batch.batch_processor - INFO - Retrying job asda_Rollback_1754249536 (attempt 1)
2025-08-03 20:34:04,343 - ecommerce_scraper.batch.batch_processor - INFO - Worker 0 stopped
2025-08-03 20:34:34,344 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:34:34,344 - ecommerce_scraper.batch.batch_processor - INFO - Stopped batch processing
2025-08-03 20:47:38,711 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:47:43,711 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:47:52,738 - ecommerce_scraper.concurrent.async_scraper - INFO - Starting concurrent scraping of 3 URLs
2025-08-03 20:47:52,743 - ecommerce_scraper.concurrent.async_scraper - INFO - Starting scrape task demo_147147905030204505: https://demo.vercel.store/products/acme-mug
2025-08-03 20:47:52,745 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:47:52,746 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:47:52,746 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:47:52,747 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:47:52,749 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:47:52,749 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:47:52,750 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:47:52,750 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:47:52,751 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:47:52,752 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 20:47:52,752 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:47:53,689 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:47:54,010 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:47:54,506 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 20:47:54,550 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:47:55,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:47:55,522 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:47:56,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:47:56,413 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:47:56,441 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:48:02,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:48:02,039 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:48:02,046 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:48:02,063 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:48:07,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:48:07,670 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:48:08,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:08,447 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:48:09,610 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:09,615 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:48:09,624 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:48:15,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:48:15,549 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:48:15,552 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:48:15,569 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:48:28,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:48:28,109 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:48:28,775 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:28,799 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:48:29,622 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:48:46,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:48:46,355 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:48:48,095 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:48,101 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:48:49,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:49,213 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:48:50,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:50,217 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:48:50,715 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:50,719 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:48:51,895 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:51,898 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:48:54,782 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:48:54,787 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:48:54,808 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:48:57,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:48:57,879 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:48:57,905 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:49:04,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:49:04,809 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:49:06,184 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:06,189 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:49:06,816 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:06,824 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:49:06,836 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:49:12,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:49:12,750 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:49:12,753 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:49:12,769 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:49:25,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:49:25,809 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:49:26,604 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:26,607 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:49:26,611 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:49:36,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:49:36,885 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:49:37,597 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:37,600 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:49:38,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:38,988 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:49:39,456 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:39,459 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:49:39,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:39,801 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:49:40,215 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:40,219 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:49:40,235 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:49:46,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:49:46,987 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:49:47,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:47,587 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:49:47,592 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:49:53,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:49:53,035 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:49:53,064 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:49:53,065 - ecommerce_scraper.concurrent.async_scraper - WARNING - Task demo_147147905030204505 completed but no products found
2025-08-03 20:49:53,067 - ecommerce_scraper.concurrent.async_scraper - INFO - Starting scrape task demo_-6511317083776043623: https://demo.vercel.store/products/acme-circles-t-shirt
2025-08-03 20:49:53,068 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 20:49:53,069 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 20:49:53,069 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 20:49:53,070 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 20:49:53,071 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:49:53,071 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 20:49:53,072 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 20:49:53,072 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:49:53,073 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 20:49:53,074 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 20:49:53,074 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 20:49:53,673 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 20:49:54,184 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 20:49:55,040 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:55,044 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:49:55,628 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:49:55,632 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:49:55,652 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:49:58,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:49:58,550 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:49:58,554 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:49:58,570 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:50:01,817 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:50:01,820 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:50:01,824 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:50:01,843 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:50:08,623 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:50:08,632 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:50:09,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:09,558 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:50:10,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:10,167 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:50:10,174 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:50:14,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:50:14,059 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:50:14,064 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:50:14,089 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:50:25,561 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:50:25,564 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:50:26,514 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:26,517 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:50:26,522 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:50:35,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:50:35,336 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:50:35,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:35,888 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:50:36,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:36,220 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:50:37,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:37,216 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:50:37,591 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:37,596 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:50:37,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:38,065 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:50:38,088 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:50:43,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:50:43,469 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:50:44,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:44,405 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:50:45,573 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:50:45,577 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:50:45,586 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:50:51,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:50:51,694 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:50:51,731 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:50:56,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:50:56,566 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:50:56,571 - ecommerce_scraper.tools.stagehand_tool - ERROR - Error executing Stagehand command: signal only works in main thread of the main interpreter
2025-08-03 20:50:56,586 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:51:05,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:51:05,485 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:51:06,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:51:06,128 - root - ERROR - Error during short_term save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:51:06,132 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:51:13,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:51:13,362 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:51:14,053 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:51:14,058 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:51:15,200 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:51:15,498 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:51:15,861 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:51:15,864 - root - ERROR - Error during entities save: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:51:15,880 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:51:19,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:51:19,380 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:51:19,400 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:51:26,592 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:51:26,602 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:51:26,607 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:51:32,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:51:32,645 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:51:33,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:51:33,442 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:51:34,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:51:34,129 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:51:34,148 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:51:38,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:51:38,709 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:51:39,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:51:39,337 - root - ERROR - Error during short_term search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/short_term/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\69207890-18b2-4b55-9830-e277da5ff455'
2025-08-03 20:51:39,933 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 20:51:39,937 - root - ERROR - Error during entities search: [WinError 3] The system cannot find the path specified: 'C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\lastAttempt/entities/Multi-Vendor_Product_Scraping_Coordinator_Multi-Vendor_Ecommerce_Site_Navigation_Expert_Multi-Vendor_Product_Data_Extraction_Specialist_StandardizedProduct_Data_Validation_Expert\\32340e3f-ce5d-4a34-ab40-a91573644877'
2025-08-03 20:51:39,956 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 20:51:43,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 20:51:43,471 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 20:51:43,493 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 20:55:56,692 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 20:56:01,455 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 20:56:18,430 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 20:56:20,521 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/380eaffe-a2c3-4c91-853c-d4f8073ad70e "HTTP/1.1 200 OK"
2025-08-03 20:56:23,351 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 20:56:25,070 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/380eaffe-a2c3-4c91-853c-d4f8073ad70e/end "HTTP/1.1 200 OK"
2025-08-03 20:56:25,129 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 20:56:25,129 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 20:56:25,336 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 22:41:37,035 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 22:41:42,648 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 22:42:53,316 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 22:42:58,486 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 22:43:14,122 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 22:43:19,237 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 22:44:02,515 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 22:44:08,130 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 22:44:14,224 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 22:44:14,226 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 22:44:14,227 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 22:44:14,227 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 22:44:14,228 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:44:14,229 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 22:44:14,231 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 22:44:14,233 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:44:14,237 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 22:44:14,238 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 22:44:14,239 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:44:14,258 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 22:44:14,259 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 22:44:14,260 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 22:44:14,260 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 22:44:14,261 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:44:14,263 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 22:44:14,264 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 22:44:14,265 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:44:14,266 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 22:44:14,267 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 22:44:14,268 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:44:14,291 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 22:44:14,292 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 22:44:14,293 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 22:44:14,294 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 22:44:14,295 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:44:14,295 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 22:44:14,296 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 22:44:14,298 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:44:14,299 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 22:44:14,300 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 22:44:14,303 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:49:33,907 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 22:49:38,251 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 22:50:19,894 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 22:50:19,895 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 22:50:19,896 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 22:50:19,897 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 22:50:19,898 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:50:19,898 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 22:50:19,899 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 22:50:19,900 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:50:19,901 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 22:50:19,903 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 22:50:19,903 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 22:50:20,747 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 22:50:21,561 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 22:50:22,143 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 22:50:22,664 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 22:50:24,171 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:50:25,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:50:25,458 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:50:31,590 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:50:31,613 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:50:35,116 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 22:50:36,750 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db "HTTP/1.1 200 OK"
2025-08-03 22:50:40,650 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/navigate "HTTP/1.1 200 OK"
2025-08-03 22:50:55,670 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:50:59,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:50:59,391 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:51:04,415 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Observe the homepage to find Rollback category lin...
2025-08-03 22:51:06,645 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/observe "HTTP/1.1 200 OK"
2025-08-03 22:51:11,162 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 22:51:11,190 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:51:14,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:51:14,736 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:51:19,749 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing act command (attempt 1): Click on the Rollback category link...
2025-08-03 22:51:21,604 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/act "HTTP/1.1 200 OK"
2025-08-03 22:51:40,027 - ecommerce_scraper.tools.stagehand_tool - INFO - Action completed
2025-08-03 22:51:40,057 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:51:44,152 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:51:44,160 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:51:49,177 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data including name, description, ...
2025-08-03 22:51:59,680 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/extract "HTTP/1.1 200 OK"
2025-08-03 22:52:03,032 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 22:52:03,086 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:52:06,670 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:52:06,674 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:52:11,694 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Observe the product listings to identify selectors...
2025-08-03 22:52:14,490 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/observe "HTTP/1.1 200 OK"
2025-08-03 22:52:18,338 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 22:52:18,368 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:52:22,834 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:52:22,836 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:52:22,851 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:52:25,974 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:52:25,992 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:52:31,006 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product name, description, price, image UR...
2025-08-03 22:52:33,667 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/extract "HTTP/1.1 200 OK"
2025-08-03 22:52:36,132 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 22:52:36,169 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:52:44,478 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:52:44,484 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:52:44,508 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:52:49,219 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:52:49,228 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:52:50,035 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:52:51,172 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:52:55,931 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:52:55,934 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:52:57,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:52:58,628 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:52:59,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:53:01,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:53:02,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:53:03,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:53:03,505 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:53:08,568 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:53:08,790 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:53:10,717 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/navigate "HTTP/1.1 200 OK"
2025-08-03 22:53:19,032 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:53:22,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:53:22,259 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:53:24,276 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data including name, description, ...
2025-08-03 22:53:26,166 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/extract "HTTP/1.1 200 OK"
2025-08-03 22:53:29,055 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 22:53:29,079 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:53:32,023 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:53:32,038 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:53:36,058 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Observe the page structure to identify product det...
2025-08-03 22:53:37,604 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/observe "HTTP/1.1 200 OK"
2025-08-03 22:53:39,268 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 22:53:39,325 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:53:42,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:53:42,755 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:53:46,786 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Preview product details section to identify necess...
2025-08-03 22:53:49,893 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/observe "HTTP/1.1 200 OK"
2025-08-03 22:53:51,593 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 22:53:51,624 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:54:00,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:54:00,205 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:54:00,225 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:54:04,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:54:04,300 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:54:04,973 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:54:05,006 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:54:08,569 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:54:08,585 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:54:09,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:54:09,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:54:09,831 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:54:12,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:54:12,620 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:54:13,020 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:54:13,048 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 22:54:17,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 22:54:17,769 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 22:54:18,461 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:54:18,824 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 22:54:18,919 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 22:54:20,407 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/bd0d29c0-85da-48df-9015-1ec8df9171db/end "HTTP/1.1 200 OK"
2025-08-03 22:54:20,594 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 22:54:20,594 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 23:06:32,536 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:06:37,256 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:13:02,494 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:13:07,140 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:13:37,355 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 23:13:37,356 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 23:13:37,356 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 23:13:37,357 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 23:13:37,358 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:13:37,358 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 23:13:37,359 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 23:13:37,362 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:13:37,363 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 23:13:37,363 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 23:13:37,364 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:13:38,219 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:13:38,539 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 23:13:39,055 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:13:39,100 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 23:13:40,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:13:41,801 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:13:41,922 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:13:47,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:13:47,877 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:13:51,664 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 23:13:54,566 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/29d9df37-6a5f-453a-95ce-fe6cf3c195e9 "HTTP/1.1 200 OK"
2025-08-03 23:14:02,490 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/29d9df37-6a5f-453a-95ce-fe6cf3c195e9/navigate "HTTP/1.1 200 OK"
2025-08-03 23:14:17,491 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:14:20,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:14:20,103 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:14:25,123 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data using the StandardizedProduct...
2025-08-03 23:14:27,082 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/29d9df37-6a5f-453a-95ce-fe6cf3c195e9/extract "HTTP/1.1 200 OK"
2025-08-03 23:14:37,539 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/29d9df37-6a5f-453a-95ce-fe6cf3c195e9/end "HTTP/1.1 200 OK"
2025-08-03 23:14:37,556 - ecommerce_scraper.tools.stagehand_tool - WARNING - Attempt 1 failed: Failed to run async operation: , cannot reuse already awaited coroutine
2025-08-03 23:14:38,557 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 2): Extract product data using the StandardizedProduct...
2025-08-03 23:18:51,490 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:18:57,111 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:19:35,161 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:19:39,954 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:20:13,629 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:20:18,647 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:20:24,684 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 23:20:24,685 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 23:20:24,685 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 23:20:24,686 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 23:20:24,686 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:20:24,687 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 23:20:24,687 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 23:20:24,688 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:20:24,689 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 23:20:24,689 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 23:20:24,690 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:20:25,614 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:20:26,023 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 23:20:26,545 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:20:26,594 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 23:20:29,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:20:31,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:20:31,495 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:20:40,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:20:40,303 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:20:44,517 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 23:20:46,107 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/33cb3b0a-0b85-4d38-b72f-89cf750522e9 "HTTP/1.1 200 OK"
2025-08-03 23:20:49,462 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/33cb3b0a-0b85-4d38-b72f-89cf750522e9/navigate "HTTP/1.1 200 OK"
2025-08-03 23:20:57,219 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:21:01,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:21:01,016 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:21:01,541 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:21:07,757 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:21:07,782 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:21:08,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:10,180 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:10,215 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:21:14,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:21:14,760 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:21:15,436 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:16,492 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:21:22,581 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:21:22,590 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:21:23,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:25,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:26,370 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:26,432 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:21:32,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:21:32,162 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:21:33,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:33,140 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:21:41,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:21:41,160 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:21:44,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:44,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:45,144 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:46,834 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:48,435 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:49,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:21:49,144 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:21:55,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:21:55,254 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:21:56,818 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/33cb3b0a-0b85-4d38-b72f-89cf750522e9/navigate "HTTP/1.1 200 OK"
2025-08-03 23:22:05,966 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:22:09,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:22:09,136 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:22:16,172 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product information including name, descri...
2025-08-03 23:22:18,405 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/33cb3b0a-0b85-4d38-b72f-89cf750522e9/extract "HTTP/1.1 200 OK"
2025-08-03 23:22:19,143 - ecommerce_scraper.tools.stagehand_tool - WARNING - Attempt 1 failed: cannot reuse already awaited coroutine
2025-08-03 23:22:20,148 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 2): Extract product information including name, descri...
2025-08-03 23:26:29,591 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/33cb3b0a-0b85-4d38-b72f-89cf750522e9/end "HTTP/1.1 200 OK"
2025-08-03 23:28:31,332 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:28:38,355 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:28:43,358 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:28:47,676 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:28:57,329 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 23:28:57,330 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 23:28:57,332 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 23:28:57,333 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 23:28:57,335 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:28:57,337 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 23:28:57,338 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 23:28:57,339 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:28:57,340 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 23:28:57,340 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 23:28:57,341 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:28:58,378 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:28:58,743 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 23:28:59,239 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:28:59,286 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 23:29:00,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:29:00,899 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:29:01,002 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:29:07,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:29:07,019 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:29:09,843 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 23:29:11,336 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/aebbcacb-d72b-4164-9899-80588e5441aa "HTTP/1.1 200 OK"
2025-08-03 23:29:14,764 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aebbcacb-d72b-4164-9899-80588e5441aa/navigate "HTTP/1.1 200 OK"
2025-08-03 23:29:22,872 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:29:27,085 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:29:27,093 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:29:32,113 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data including name, description, ...
2025-08-03 23:29:34,118 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aebbcacb-d72b-4164-9899-80588e5441aa/extract "HTTP/1.1 200 OK"
2025-08-03 23:29:45,853 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 23:29:45,879 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:30:18,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:30:18,608 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:30:18,670 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:30:59,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:30:59,035 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:30:59,079 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:31:05,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:31:05,322 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:31:07,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:31:09,384 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:31:09,440 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:31:13,123 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:31:13,126 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:31:13,185 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:31:18,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:31:18,116 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:31:18,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:31:19,645 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:31:26,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:31:26,833 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:31:27,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:31:28,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:31:29,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:31:29,273 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:31:59,998 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:32:00,011 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:32:00,034 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:32:24,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:32:24,486 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:32:26,765 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:26,802 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:32:35,743 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:32:35,751 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:32:37,930 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:39,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:41,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:42,383 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:43,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:44,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:45,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:47,220 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:32:47,471 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:33:10,757 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:33:10,759 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:33:12,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:12,691 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:33:20,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:33:20,096 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:33:20,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:21,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:21,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:22,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:22,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:23,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:25,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:26,141 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:33:26,197 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:33:38,727 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:33:38,731 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:33:38,794 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:33:51,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:33:51,651 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:33:51,707 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:34:04,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:34:04,377 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:34:04,463 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:34:18,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:34:18,693 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:34:18,730 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:34:32,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:34:32,112 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:34:33,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:34:33,753 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:34:45,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:34:45,677 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:34:46,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:34:46,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:34:47,792 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:34:48,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:34:48,543 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:34:50,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:34:50,262 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 23:34:52,217 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aebbcacb-d72b-4164-9899-80588e5441aa/end "HTTP/1.1 200 OK"
2025-08-03 23:34:52,353 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 23:34:52,354 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 23:54:37,595 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:54:42,748 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:54:48,090 - ai_logger_scraper_20250803_235448 - INFO - AI Logger initialized for session: scraper_20250803_235448
2025-08-03 23:54:48,097 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 23:54:48,098 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 23:54:48,099 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 23:54:48,100 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 23:54:48,102 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:54:48,103 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 23:54:48,104 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 23:54:48,104 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:54:48,105 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 23:54:48,106 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 23:54:48,107 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:54:49,149 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:54:49,542 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 23:54:50,056 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:54:50,114 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 23:54:50,121 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 23:54:50,125 - ai_logger_scraper_20250803_235448 - INFO - TOOL [Multi-Vendor Product Scraping Coordinator] ecommerce_stagehand_tool.close: SUCCESS
2025-08-03 23:54:50,136 - ai_logger_scraper_20250803_235448 - INFO - Session summary saved: logs\scraper_20250803_235448\session_summary.json
2025-08-03 23:54:50,137 - ai_logger_scraper_20250803_235448 - INFO - AI Logger session closed: scraper_20250803_235448
2025-08-03 23:54:50,806 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 23:54:50,807 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 23:56:32,629 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 23:56:37,752 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 23:56:43,232 - ai_logger_scraper_20250803_235643 - INFO - AI Logger initialized for session: scraper_20250803_235643
2025-08-03 23:56:43,238 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-03 23:56:43,238 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-03 23:56:43,239 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-03 23:56:43,239 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-03 23:56:43,240 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:56:43,241 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-03 23:56:43,242 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-03 23:56:43,243 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:56:43,243 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-03 23:56:43,244 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-03 23:56:43,245 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-03 23:56:44,257 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:56:44,603 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 23:56:45,140 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 23:56:45,195 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 23:56:45,206 - ai_logger_scraper_20250803_235643 - INFO - TASK [CrewAI_System] crew_category_20250803_235643: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-03 23:56:46,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:56:47,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 23:56:47,786 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:56:56,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:56:56,379 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:56:59,240 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 23:57:00,642 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/f145ebf4-b095-46fd-b720-6d7e353bba79 "HTTP/1.1 200 OK"
2025-08-03 23:57:40,627 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f145ebf4-b095-46fd-b720-6d7e353bba79/navigate "HTTP/1.1 200 OK"
2025-08-03 23:57:49,635 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:57:53,079 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 23:57:53,084 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 23:57:55,097 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data including name, description, ...
2025-08-03 23:58:01,573 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f145ebf4-b095-46fd-b720-6d7e353bba79/extract "HTTP/1.1 200 OK"
2025-08-03 23:58:18,405 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 23:58:18,434 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 23:58:51,561 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f145ebf4-b095-46fd-b720-6d7e353bba79/end "HTTP/1.1 200 OK"
2025-08-04 00:05:51,758 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:05:56,281 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:06:45,237 - ai_logger_scraper_20250804_000645 - INFO - AI Logger initialized for session: scraper_20250804_000645
2025-08-04 00:06:45,243 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:06:45,244 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:06:45,245 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:06:45,245 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:06:45,246 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:06:45,247 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:06:45,248 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:06:45,249 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:06:45,250 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:06:45,251 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:06:45,252 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:06:46,201 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:06:46,566 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:06:47,091 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:06:47,138 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:06:47,144 - ai_logger_scraper_20250804_000645 - INFO - TASK [CrewAI_System] crew_category_20250804_000645: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:06:48,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:06:49,401 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:06:49,499 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:06:56,945 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:06:56,960 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:06:59,850 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 00:07:03,348 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/d132420c-e158-463e-92e3-a6932193f378 "HTTP/1.1 200 OK"
2025-08-04 00:07:09,369 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/d132420c-e158-463e-92e3-a6932193f378/navigate "HTTP/1.1 200 OK"
2025-08-04 00:07:17,135 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:07:22,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:07:22,312 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:07:27,327 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product information using StandardizedProd...
2025-08-04 00:07:29,175 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/d132420c-e158-463e-92e3-a6932193f378/extract "HTTP/1.1 200 OK"
2025-08-04 00:07:29,930 - ecommerce_scraper.tools.stagehand_tool - WARNING - Attempt 1 failed: cannot reuse already awaited coroutine
2025-08-04 00:07:30,933 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 2): Extract product information using StandardizedProd...
2025-08-04 00:10:29,087 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/d132420c-e158-463e-92e3-a6932193f378/end "HTTP/1.1 410 Gone"
2025-08-04 00:11:07,418 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:11:16,495 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:11:25,182 - ai_logger_scraper_20250804_001125 - INFO - AI Logger initialized for session: scraper_20250804_001125
2025-08-04 00:11:25,196 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:11:25,197 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:11:25,198 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:11:25,199 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:11:25,200 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:11:25,201 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:11:25,203 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:11:25,206 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:11:25,207 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:11:25,209 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:11:25,210 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:11:26,403 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:11:26,849 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:11:27,475 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:11:27,527 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:11:27,537 - ai_logger_scraper_20250804_001125 - INFO - TASK [CrewAI_System] crew_category_20250804_001125: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:11:28,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:11:29,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:11:29,946 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:11:39,719 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:11:39,731 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:11:43,359 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 00:11:45,063 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/1f10fae7-38a4-4c86-9ca7-fec3e9c545cd "HTTP/1.1 200 OK"
2025-08-04 00:11:48,347 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/1f10fae7-38a4-4c86-9ca7-fec3e9c545cd/navigate "HTTP/1.1 200 OK"
2025-08-04 00:11:56,562 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:12:01,485 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:12:01,492 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:12:01,546 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:12:07,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:12:07,051 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:12:09,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:12:10,418 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:12:10,573 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:12:13,308 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:12:13,451 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:12:13,522 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:12:19,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:12:19,024 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:12:19,081 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:12:23,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:12:23,837 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:12:23,898 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:12:33,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:12:33,936 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:12:33,956 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:12:48,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:12:48,369 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:12:49,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:12:51,465 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:13:06,024 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:13:06,047 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:13:07,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:07,527 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:07,951 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:08,442 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:09,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:09,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:09,850 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:10,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:10,367 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:13:44,771 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:13:44,899 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:13:46,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:13:47,795 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:13:59,145 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:13:59,169 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:14:00,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:02,168 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:03,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:04,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:05,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:06,334 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:07,318 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:07,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:08,011 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:14:35,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:14:35,384 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:14:38,114 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:38,145 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:14:48,757 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:14:48,765 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:14:50,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:51,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:51,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:52,865 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:54,169 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:55,151 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:55,813 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:56,306 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:14:56,378 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:15:07,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:15:07,052 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:15:07,245 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:15:17,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:15:17,243 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:15:17,319 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:15:26,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:15:26,948 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:15:27,067 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:15:30,806 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:15:30,809 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:15:30,842 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:15:37,200 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:15:37,213 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:15:39,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:39,113 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:15:51,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:15:51,395 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:15:52,146 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:53,026 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:54,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:54,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:55,280 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:55,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:56,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:56,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:56,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:15:57,020 - ai_logger_scraper_20250804_001125 - INFO - TASK [CrewAI_System] crew_category_20250804_001125: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 00:15:57,027 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-04 00:15:59,381 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/1f10fae7-38a4-4c86-9ca7-fec3e9c545cd/end "HTTP/1.1 200 OK"
2025-08-04 00:16:06,158 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-04 00:16:06,160 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-04 00:16:06,168 - ai_logger_scraper_20250804_001125 - INFO - Session summary saved: logs\scraper_20250804_001125\session_summary.json
2025-08-04 00:16:06,170 - ai_logger_scraper_20250804_001125 - INFO - AI Logger session closed: scraper_20250804_001125
2025-08-04 00:17:36,517 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:17:41,598 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:17:47,134 - ai_logger_scraper_20250804_001747 - INFO - AI Logger initialized for session: scraper_20250804_001747
2025-08-04 00:17:47,141 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:17:47,142 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:17:47,143 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:17:47,144 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:17:47,144 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:17:47,145 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:17:47,145 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:17:47,146 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:17:47,147 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:17:47,148 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:17:47,149 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:17:48,053 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:17:48,424 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:17:48,956 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:17:49,002 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:17:49,010 - ai_logger_scraper_20250804_001747 - INFO - TASK [CrewAI_System] crew_category_20250804_001747: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:17:50,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:17:52,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:17:52,185 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:17:57,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:17:57,848 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:17:58,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:17:59,576 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:18:05,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:18:05,487 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:18:06,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:18:07,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:18:07,770 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:18:13,993 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:18:13,998 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:18:14,710 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:18:14,734 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:18:18,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:18:18,139 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:18:18,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:18:19,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:18:19,542 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:18:29,483 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:18:29,485 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:18:29,534 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:18:41,712 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:18:41,714 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:18:41,741 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:18:53,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:18:53,608 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:18:53,661 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:19:07,897 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:19:07,914 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:19:07,938 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:19:21,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:19:21,095 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:19:22,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:22,833 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:19:30,892 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:19:30,901 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:19:31,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:33,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:34,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:34,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:36,157 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:36,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:37,140 - ai_logger_scraper_20250804_001747 - INFO - TASK [CrewAI_System] crew_category_20250804_001747: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 00:19:37,145 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 00:19:37,151 - ai_logger_scraper_20250804_001747 - INFO - Session summary saved: logs\scraper_20250804_001747\session_summary.json
2025-08-04 00:19:37,152 - ai_logger_scraper_20250804_001747 - INFO - AI Logger session closed: scraper_20250804_001747
2025-08-04 00:19:39,045 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:19:43,684 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:19:48,830 - ai_logger_scraper_20250804_001948 - INFO - AI Logger initialized for session: scraper_20250804_001948
2025-08-04 00:19:48,837 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:19:48,838 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:19:48,839 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:19:48,840 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:19:48,840 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:19:48,841 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:19:48,842 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:19:48,843 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:19:48,843 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:19:48,844 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:19:48,845 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:19:49,750 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:19:50,087 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:19:50,701 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:19:50,750 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:19:50,757 - ai_logger_scraper_20250804_001948 - INFO - TASK [CrewAI_System] crew_category_20250804_001948: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:19:51,592 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:53,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:19:53,534 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:20:03,894 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:20:03,906 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:20:06,812 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 00:20:08,595 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/af9c2ca3-0b31-4468-b2a4-f3e91d80d0f4 "HTTP/1.1 200 OK"
2025-08-04 00:20:12,213 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/af9c2ca3-0b31-4468-b2a4-f3e91d80d0f4/navigate "HTTP/1.1 200 OK"
2025-08-04 00:20:23,506 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:20:39,290 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:20:39,292 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:20:44,304 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data using StandardizedProduct sch...
2025-08-04 00:20:47,359 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/af9c2ca3-0b31-4468-b2a4-f3e91d80d0f4/extract "HTTP/1.1 200 OK"
2025-08-04 00:21:01,017 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 00:21:01,037 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:21:18,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:21:18,272 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:21:18,306 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:21:35,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:21:35,697 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:21:36,993 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:21:37,577 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:21:37,614 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:21:41,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:21:41,529 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:21:41,560 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:21:45,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:21:45,845 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:21:45,890 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:21:51,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:21:51,642 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:21:51,693 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:21:58,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:21:58,765 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:21:58,785 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:22:06,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:22:06,808 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:22:07,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:08,360 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:22:22,704 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:22:22,714 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:22:24,173 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:24,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:25,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:27,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:27,861 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:28,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:30,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:31,241 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:22:31,305 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:23:01,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:23:01,687 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:23:03,291 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:03,323 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:23:14,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:23:14,015 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:23:15,181 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:16,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:17,729 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:19,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:19,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:20,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:21,980 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:23,313 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:24,332 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:24,395 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:23:57,010 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:23:57,019 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:23:57,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:23:57,831 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:24:09,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:24:09,502 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:24:11,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:12,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:13,278 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:13,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:14,630 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:15,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:16,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:17,141 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:18,105 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:24:18,206 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:24:26,990 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:24:26,991 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:24:27,049 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:24:35,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:24:35,926 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:24:35,972 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:24:44,670 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:24:44,679 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:24:44,734 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:24:54,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:24:54,694 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:24:54,721 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:25:05,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:25:05,234 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:25:06,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:06,284 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:25:17,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:25:17,803 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:25:18,471 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:18,819 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:19,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:20,035 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:20,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:21,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:21,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:22,975 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:25:23,197 - ai_logger_scraper_20250804_001948 - INFO - TASK [CrewAI_System] crew_category_20250804_001948: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 00:25:23,202 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-04 00:25:25,291 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/af9c2ca3-0b31-4468-b2a4-f3e91d80d0f4/end "HTTP/1.1 200 OK"
2025-08-04 00:25:25,391 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-04 00:25:25,391 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-04 00:25:25,407 - ai_logger_scraper_20250804_001948 - INFO - Session summary saved: logs\scraper_20250804_001948\session_summary.json
2025-08-04 00:25:25,407 - ai_logger_scraper_20250804_001948 - INFO - AI Logger session closed: scraper_20250804_001948
2025-08-04 00:26:19,593 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:26:24,125 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:26:29,447 - ai_logger_scraper_20250804_002629 - INFO - AI Logger initialized for session: scraper_20250804_002629
2025-08-04 00:26:29,454 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:26:29,455 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:26:29,456 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:26:29,457 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:26:29,458 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:26:29,458 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:26:29,459 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:26:29,460 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:26:29,461 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:26:29,461 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:26:29,463 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:26:29,481 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 00:26:29,490 - ai_logger_scraper_20250804_002629 - INFO - Session summary saved: logs\scraper_20250804_002629\session_summary.json
2025-08-04 00:26:29,491 - ai_logger_scraper_20250804_002629 - INFO - AI Logger session closed: scraper_20250804_002629
2025-08-04 00:26:29,634 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 00:26:29,635 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 00:27:23,393 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:27:28,030 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:27:33,241 - ai_logger_scraper_20250804_002733 - INFO - AI Logger initialized for session: scraper_20250804_002733
2025-08-04 00:27:33,249 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:27:33,249 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:27:33,250 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:27:33,251 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:27:33,252 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:27:33,252 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:27:33,253 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:27:33,254 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:27:33,255 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:27:33,256 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:27:33,258 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:27:34,175 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:27:34,624 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:27:35,136 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:27:35,184 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:27:35,191 - ai_logger_scraper_20250804_002733 - INFO - TASK [CrewAI_System] crew_category_20250804_002733: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:27:36,609 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:27:38,056 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:27:38,310 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:27:45,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:27:46,000 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:27:48,941 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 00:27:50,433 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/2476395c-0911-4054-b11f-83bb0a774dc1 "HTTP/1.1 200 OK"
2025-08-04 00:27:53,673 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/2476395c-0911-4054-b11f-83bb0a774dc1/navigate "HTTP/1.1 200 OK"
2025-08-04 00:28:01,601 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:28:07,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:28:07,841 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:28:12,937 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data including name, description, ...
2025-08-04 00:28:14,845 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/2476395c-0911-4054-b11f-83bb0a774dc1/extract "HTTP/1.1 200 OK"
2025-08-04 00:28:33,373 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 00:28:33,408 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:28:57,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:28:57,101 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:28:57,210 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:29:23,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:29:23,460 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:29:23,500 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:29:51,209 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:29:51,214 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:29:52,718 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:29:54,143 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:29:54,175 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:30:04,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:30:04,168 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:30:04,205 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:30:08,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:30:08,883 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:30:09,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:30:10,461 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:30:17,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:30:17,364 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:30:18,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:30:18,609 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:30:18,953 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:30:19,010 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:30:52,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:30:52,895 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:30:52,913 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:31:23,170 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:31:23,174 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:31:25,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:25,274 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:31:37,775 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:31:37,777 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:31:38,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:39,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:39,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:40,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:41,383 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:42,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:42,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:46,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:46,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:31:46,490 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:32:09,636 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:32:09,646 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:32:10,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:10,510 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:32:21,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:32:21,662 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:32:23,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:23,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:24,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:25,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:25,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:27,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:29,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:29,483 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:32:29,525 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:32:42,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:32:42,421 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:32:42,480 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:32:54,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:32:54,549 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:32:54,595 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:33:09,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:33:09,087 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:33:11,348 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:11,377 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:33:22,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:33:22,439 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:33:23,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:23,672 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:24,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:24,390 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:24,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:25,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:25,472 - ai_logger_scraper_20250804_002733 - INFO - TASK [CrewAI_System] crew_category_20250804_002733: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 00:33:25,502 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-04 00:33:27,528 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/2476395c-0911-4054-b11f-83bb0a774dc1/end "HTTP/1.1 200 OK"
2025-08-04 00:33:27,578 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-04 00:33:27,578 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-04 00:33:27,596 - ai_logger_scraper_20250804_002733 - INFO - Session summary saved: logs\scraper_20250804_002733\session_summary.json
2025-08-04 00:33:27,597 - ai_logger_scraper_20250804_002733 - INFO - AI Logger session closed: scraper_20250804_002733
2025-08-04 00:33:29,413 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:33:34,851 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:33:40,938 - ai_logger_scraper_20250804_003340 - INFO - AI Logger initialized for session: scraper_20250804_003340
2025-08-04 00:33:40,945 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:33:40,946 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:33:40,946 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:33:40,947 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:33:40,948 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:33:40,949 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:33:40,950 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:33:40,951 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:33:40,951 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:33:40,952 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:33:40,953 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:33:41,932 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:33:42,358 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:33:43,021 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:33:43,067 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:33:43,073 - ai_logger_scraper_20250804_003340 - INFO - TASK [CrewAI_System] crew_category_20250804_003340: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:33:44,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:45,145 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:33:45,268 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:33:51,145 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:33:51,158 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:33:54,144 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 00:33:55,848 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/a7446d95-42bc-49e4-b8c2-1e2a21718abc "HTTP/1.1 200 OK"
2025-08-04 00:33:59,653 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a7446d95-42bc-49e4-b8c2-1e2a21718abc/navigate "HTTP/1.1 200 OK"
2025-08-04 00:34:07,334 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:34:12,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:34:12,281 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:34:17,292 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data including name, description, ...
2025-08-04 00:34:19,301 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a7446d95-42bc-49e4-b8c2-1e2a21718abc/extract "HTTP/1.1 200 OK"
2025-08-04 00:34:30,001 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 00:34:30,021 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:34:58,416 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:34:58,425 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:34:58,484 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:35:05,002 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:35:05,010 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:35:05,682 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:06,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:06,357 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:35:20,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:35:20,945 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:35:20,999 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:35:35,786 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:35:35,788 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:35:36,560 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:37,647 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:35:49,242 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:35:49,251 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:35:50,242 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:50,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:51,180 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:51,542 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:52,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:53,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:53,386 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:53,739 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:35:53,814 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:36:17,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:36:17,880 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:36:18,912 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:18,957 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:36:31,605 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:36:31,614 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:36:32,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:33,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:34,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:34,457 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:36,126 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:36,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:38,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:39,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:40,735 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:41,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:36:41,187 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:37:04,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:37:04,932 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:37:06,248 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:06,282 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:37:16,766 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:37:16,773 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:37:18,223 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:19,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:20,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:21,555 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:22,515 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:23,487 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:24,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:25,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:37:25,578 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:37:45,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:37:45,282 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:37:45,359 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:44:21,812 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:44:31,271 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:44:37,479 - ai_logger_scraper_20250804_004437 - INFO - AI Logger initialized for session: scraper_20250804_004437
2025-08-04 00:44:37,488 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:44:37,488 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:44:37,489 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:44:37,490 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:44:37,491 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:44:37,492 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:44:37,492 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:44:37,493 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:44:37,494 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:44:37,495 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:44:37,495 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:44:38,510 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:44:38,910 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:44:39,437 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:44:39,486 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:44:39,493 - ai_logger_scraper_20250804_004437 - INFO - TASK [CrewAI_System] crew_category_20250804_004437: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:44:40,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:44:41,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:44:41,583 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:44:49,897 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:44:49,912 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:44:53,111 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 00:44:54,650 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/80d1a779-b9d0-4d87-aa4f-9adfd3521413 "HTTP/1.1 200 OK"
2025-08-04 00:44:59,453 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/80d1a779-b9d0-4d87-aa4f-9adfd3521413/navigate "HTTP/1.1 200 OK"
2025-08-04 00:45:07,480 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:45:11,534 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:45:11,536 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:45:16,550 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data using the StandardizedProduct...
2025-08-04 00:45:19,850 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/80d1a779-b9d0-4d87-aa4f-9adfd3521413/extract "HTTP/1.1 200 OK"
2025-08-04 00:45:29,511 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 00:45:29,539 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:45:39,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:45:39,313 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:45:39,363 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:45:46,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:45:46,149 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:45:47,179 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:45:48,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:45:48,400 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:45:51,730 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:45:51,745 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:45:51,784 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:45:57,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:45:57,035 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:45:57,097 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:46:04,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:46:05,000 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:46:05,068 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:46:31,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:46:32,011 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:46:32,031 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:46:55,040 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:46:55,045 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:46:56,117 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:46:57,441 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:47:19,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:47:19,488 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:47:20,698 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:21,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:22,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:22,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:23,610 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:24,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:25,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:25,418 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:47:44,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:47:44,813 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:47:46,066 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:46,117 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:47:55,527 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:47:55,534 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:47:56,416 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:57,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:58,125 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:59,041 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:47:59,959 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:00,377 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:00,809 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:01,891 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:02,397 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:03,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:04,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:04,534 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:48:28,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:48:28,281 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:48:29,075 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:29,116 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:48:38,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:48:38,713 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:48:39,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:39,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:40,378 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:41,394 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:42,436 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:47,969 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:50,110 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:51,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:52,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:48:52,737 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:49:03,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:49:03,904 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:49:03,991 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:49:22,982 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:49:22,988 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:49:24,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:24,056 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:49:34,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:49:34,749 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:49:35,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:36,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:37,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:37,579 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:37,933 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:38,297 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:38,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:40,178 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:49:40,330 - ai_logger_scraper_20250804_004437 - INFO - TASK [CrewAI_System] crew_category_20250804_004437: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 00:49:40,367 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-04 00:49:42,396 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/80d1a779-b9d0-4d87-aa4f-9adfd3521413/end "HTTP/1.1 200 OK"
2025-08-04 00:49:42,832 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-04 00:49:42,833 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-04 00:49:42,839 - ai_logger_scraper_20250804_004437 - INFO - Session summary saved: logs\scraper_20250804_004437\session_summary.json
2025-08-04 00:49:42,839 - ai_logger_scraper_20250804_004437 - INFO - AI Logger session closed: scraper_20250804_004437
2025-08-04 00:50:05,226 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:50:11,746 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:50:19,433 - ai_logger_scraper_20250804_005019 - INFO - AI Logger initialized for session: scraper_20250804_005019
2025-08-04 00:50:19,446 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:50:19,447 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:50:19,450 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:50:19,451 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:50:19,452 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:50:19,456 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:50:19,460 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:50:19,463 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:50:19,464 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:50:19,465 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:50:19,466 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:50:20,624 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:50:21,033 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:50:21,711 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:50:21,775 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:50:21,785 - ai_logger_scraper_20250804_005019 - INFO - TASK [CrewAI_System] crew_category_20250804_005019: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:50:22,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:50:24,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:50:24,409 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:50:48,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:50:48,375 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:50:50,389 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:50:51,359 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:51:00,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:51:00,079 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:51:00,906 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:01,296 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:01,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:02,156 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:02,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:02,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:03,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:04,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:06,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:07,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:07,206 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:51:26,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:51:26,859 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:51:32,415 - openai._base_client - INFO - Retrying request to /embeddings in 0.444041 seconds
2025-08-04 00:51:33,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:33,819 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:51:44,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:51:44,128 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:51:48,049 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:49,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:50,579 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:51,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:52,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:53,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:54,917 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:56,527 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:51:56,727 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:52:08,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:52:08,516 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:52:08,570 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:52:19,491 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:52:19,495 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:52:19,532 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:52:29,713 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:52:29,716 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:52:29,771 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:52:47,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:52:47,593 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:52:47,613 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:53:06,833 - openai._base_client - INFO - Retrying request to /chat/completions in 0.403555 seconds
2025-08-04 00:53:18,901 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:53:18,924 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:53:19,733 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:20,125 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:53:35,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:53:35,432 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:53:37,277 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:38,823 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:39,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:40,984 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:42,349 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:45,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:45,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:46,888 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:53:46,990 - ai_logger_scraper_20250804_005019 - INFO - TASK [CrewAI_System] crew_category_20250804_005019: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 00:53:47,018 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 00:53:47,025 - ai_logger_scraper_20250804_005019 - INFO - Session summary saved: logs\scraper_20250804_005019\session_summary.json
2025-08-04 00:53:47,025 - ai_logger_scraper_20250804_005019 - INFO - AI Logger session closed: scraper_20250804_005019
2025-08-04 00:56:17,301 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 00:56:22,380 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 00:56:28,381 - ai_logger_scraper_20250804_005628 - INFO - AI Logger initialized for session: scraper_20250804_005628
2025-08-04 00:56:28,387 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 00:56:28,388 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 00:56:28,388 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 00:56:28,389 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 00:56:28,390 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:56:28,391 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 00:56:28,392 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 00:56:28,393 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:56:28,394 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 00:56:28,395 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 00:56:28,397 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 00:56:29,357 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:56:29,721 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 00:56:30,218 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 00:56:30,267 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 00:56:30,272 - ai_logger_scraper_20250804_005628 - INFO - TASK [CrewAI_System] crew_category_20250804_005628: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 00:56:31,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:56:32,542 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:56:32,651 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:56:56,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:56:56,657 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:56:58,000 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:56:58,925 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:57:07,568 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:57:07,575 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:57:08,929 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:09,387 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:10,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:11,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:12,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:12,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:13,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:14,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:15,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 00:57:15,544 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:57:22,118 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:57:22,121 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:57:25,011 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 00:57:26,563 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/f9a6c428-4614-4d4d-8ac0-9ef9dea6630d "HTTP/1.1 200 OK"
2025-08-04 00:57:29,937 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f9a6c428-4614-4d4d-8ac0-9ef9dea6630d/navigate "HTTP/1.1 200 OK"
2025-08-04 00:57:37,698 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:57:41,358 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:57:41,367 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:57:48,384 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product names, descriptions, prices, image...
2025-08-04 00:57:51,653 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f9a6c428-4614-4d4d-8ac0-9ef9dea6630d/extract "HTTP/1.1 200 OK"
2025-08-04 00:57:54,799 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 00:57:54,826 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:57:58,897 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:57:58,899 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:58:03,920 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Identify and analyze elements on the Asda Fruit ca...
2025-08-04 00:58:05,681 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f9a6c428-4614-4d4d-8ac0-9ef9dea6630d/observe "HTTP/1.1 200 OK"
2025-08-04 00:58:12,763 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-04 00:58:12,800 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 00:58:17,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 00:58:17,634 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 00:58:24,692 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all product names, descriptions, prices, p...
2025-08-04 00:58:26,331 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f9a6c428-4614-4d4d-8ac0-9ef9dea6630d/extract "HTTP/1.1 200 OK"
2025-08-04 00:58:27,122 - ecommerce_scraper.tools.stagehand_tool - WARNING - Attempt 1 failed: cannot reuse already awaited coroutine
2025-08-04 00:58:27,122 - ecommerce_scraper.tools.stagehand_tool - INFO - Retrying in 1 seconds...
2025-08-04 00:58:28,126 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 2): Extract all product names, descriptions, prices, p...
2025-08-04 01:05:11,728 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f9a6c428-4614-4d4d-8ac0-9ef9dea6630d/end "HTTP/1.1 410 Gone"
2025-08-04 01:40:12,609 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 01:40:30,093 - ai_logger_scraper_20250804_014030 - INFO - AI Logger initialized for session: scraper_20250804_014030
2025-08-04 01:40:30,099 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 01:40:30,099 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 01:40:30,100 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 01:40:30,100 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 01:40:30,101 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 01:40:30,101 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 01:40:30,102 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 01:40:30,105 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 01:40:30,107 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 01:40:30,108 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 01:40:30,109 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 01:40:31,160 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 01:40:31,712 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 01:40:32,291 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 01:40:32,347 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 01:40:32,356 - ai_logger_scraper_20250804_014030 - INFO - TASK [CrewAI_System] crew_category_20250804_014030: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 01:40:33,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:40:33,990 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:40:34,141 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:41:30,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:41:30,025 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:41:30,175 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:41:40,762 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:41:40,765 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:41:41,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:41:42,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:41:42,625 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:41:50,681 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:41:50,683 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:41:50,717 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:42:02,760 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:42:02,773 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:42:02,810 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:42:07,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:42:07,067 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:42:07,105 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:42:20,291 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:42:20,304 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:42:20,332 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:42:32,317 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:42:32,332 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:42:33,293 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:34,332 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:42:47,738 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:42:47,742 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:42:49,529 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:50,170 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:51,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:52,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:52,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:53,393 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:53,812 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:54,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:54,895 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:42:55,072 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:43:35,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:43:35,935 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:43:38,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:43:38,335 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:43:56,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:43:56,625 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:43:58,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:43:59,219 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:43:59,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:00,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:00,732 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:01,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:01,615 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:02,066 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:02,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:04,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:04,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:44:04,635 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:44:10,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:44:10,674 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:44:13,842 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 01:44:15,616 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/1b13eedb-02f8-44df-a34e-69a6a2abb4a5 "HTTP/1.1 200 OK"
2025-08-04 01:44:20,562 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/1b13eedb-02f8-44df-a34e-69a6a2abb4a5/navigate "HTTP/1.1 200 OK"
2025-08-04 01:44:30,696 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:45:03,765 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:45:03,776 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:45:04,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:04,772 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:45:16,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:45:16,856 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:45:18,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:19,137 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:20,726 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:21,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:22,543 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:23,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:24,195 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:24,742 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:45:24,874 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:45:38,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:45:38,024 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:45:38,268 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:45:48,565 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:45:48,582 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:45:48,676 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:46:03,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:46:03,450 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:46:03,549 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:46:16,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:46:16,928 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:46:16,974 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:47:56,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:47:56,051 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:47:56,896 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:47:56,950 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 01:48:10,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 01:48:10,181 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 01:48:10,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:48:11,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:48:11,754 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:48:12,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:48:12,516 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:48:12,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 01:48:13,176 - ai_logger_scraper_20250804_014030 - INFO - TASK [CrewAI_System] crew_category_20250804_014030: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 01:48:13,190 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 01:48:13,200 - ai_logger_scraper_20250804_014030 - INFO - Session summary saved: logs\scraper_20250804_014030\session_summary.json
2025-08-04 01:48:13,202 - ai_logger_scraper_20250804_014030 - INFO - AI Logger session closed: scraper_20250804_014030
2025-08-04 02:01:03,594 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 02:01:09,509 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 02:01:16,204 - ai_logger_scraper_20250804_020116 - INFO - AI Logger initialized for session: scraper_20250804_020116
2025-08-04 02:01:16,210 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 02:01:16,211 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 02:01:16,212 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 02:01:16,213 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 02:01:16,214 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:01:16,215 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 02:01:16,216 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 02:01:16,217 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:01:16,219 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 02:01:16,221 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 02:01:16,222 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:01:17,330 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:01:18,156 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 02:01:18,764 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:01:18,813 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 02:01:18,820 - ai_logger_scraper_20250804_020116 - INFO - TASK [CrewAI_System] crew_category_20250804_020116: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 02:01:19,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:01:20,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:01:20,749 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:01:37,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:01:37,879 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:01:37,966 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:01:57,010 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:01:57,158 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:01:58,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 503 Service Unavailable"
2025-08-04 02:01:58,723 - openai._base_client - INFO - Retrying request to /embeddings in 0.405331 seconds
2025-08-04 02:01:59,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:00,516 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:02:16,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:02:16,949 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:02:18,277 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:19,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:20,696 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:22,210 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:24,590 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:25,732 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:26,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:27,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:31,515 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:33,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:02:33,845 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:02:50,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 502 Bad Gateway"
2025-08-04 02:02:50,250 - openai._base_client - INFO - Retrying request to /chat/completions in 0.488877 seconds
2025-08-04 02:03:00,812 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:03:00,824 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:03:03,823 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 02:03:05,411 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/0ae3c0a0-38aa-491e-90d2-2beda7104f8a "HTTP/1.1 200 OK"
2025-08-04 02:03:15,716 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/0ae3c0a0-38aa-491e-90d2-2beda7104f8a/navigate "HTTP/1.1 200 OK"
2025-08-04 02:03:26,808 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:03:33,140 - openai._base_client - INFO - Retrying request to /chat/completions in 0.499534 seconds
2025-08-04 02:03:41,738 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:03:41,749 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:03:43,776 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all visible products in the Fruit category...
2025-08-04 02:03:50,916 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/0ae3c0a0-38aa-491e-90d2-2beda7104f8a/extract "HTTP/1.1 200 OK"
2025-08-04 02:04:09,534 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 02:04:09,557 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:04:13,295 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:04:13,301 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:04:15,321 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Preview the structure of the product list on the p...
2025-08-04 02:04:16,965 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/0ae3c0a0-38aa-491e-90d2-2beda7104f8a/observe "HTTP/1.1 200 OK"
2025-08-04 02:04:25,569 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-04 02:04:25,614 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:04:29,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:04:29,663 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:04:31,692 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Observe the structure of the product list specific...
2025-08-04 02:04:33,248 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/0ae3c0a0-38aa-491e-90d2-2beda7104f8a/observe "HTTP/1.1 200 OK"
2025-08-04 02:04:37,208 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-04 02:04:37,233 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:05:04,681 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:05:04,689 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:05:04,713 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:05:33,526 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:05:33,529 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:05:35,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:35,239 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:05:43,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:05:43,006 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:05:44,140 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:45,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:45,728 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:46,261 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:46,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:47,210 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:48,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:49,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:05:49,190 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:06:09,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:06:09,144 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:06:09,207 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:06:24,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:06:24,389 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:06:24,582 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:06:40,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:06:40,014 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:06:40,094 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:06:58,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:06:58,019 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:06:58,056 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:07:15,521 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:07:15,527 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:07:17,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:17,345 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:07:26,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:07:26,329 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:07:27,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:28,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:29,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:30,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:31,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:32,152 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:32,311 - ai_logger_scraper_20250804_020116 - INFO - TASK [CrewAI_System] crew_category_20250804_020116: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 02:07:32,318 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 02:07:32,333 - ai_logger_scraper_20250804_020116 - INFO - Session summary saved: logs\scraper_20250804_020116\session_summary.json
2025-08-04 02:07:32,334 - ai_logger_scraper_20250804_020116 - INFO - AI Logger session closed: scraper_20250804_020116
2025-08-04 02:07:34,765 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 02:07:40,810 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 02:07:48,729 - ai_logger_scraper_20250804_020748 - INFO - AI Logger initialized for session: scraper_20250804_020748
2025-08-04 02:07:48,737 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 02:07:48,737 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 02:07:48,738 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 02:07:48,739 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 02:07:48,739 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:07:48,740 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 02:07:48,741 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 02:07:48,742 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:07:48,744 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 02:07:48,745 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 02:07:48,746 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:07:49,814 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:07:50,317 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 02:07:50,991 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:07:51,044 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 02:07:51,050 - ai_logger_scraper_20250804_020748 - INFO - TASK [CrewAI_System] crew_category_20250804_020748: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 02:07:52,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:53,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:07:53,911 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:08:24,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:08:24,363 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:08:32,357 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:33,557 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:08:44,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:08:44,630 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:08:45,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:47,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:47,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:48,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:48,887 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:49,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:50,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:51,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:08:51,734 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:08:59,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:08:59,872 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:09:03,028 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 02:09:04,706 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/e637f6d3-1816-4508-99b9-2a3048e217f9 "HTTP/1.1 200 OK"
2025-08-04 02:09:07,836 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/e637f6d3-1816-4508-99b9-2a3048e217f9/navigate "HTTP/1.1 200 OK"
2025-08-04 02:09:16,035 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:09:19,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:09:19,501 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:09:21,520 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all visible product data using the Standar...
2025-08-04 02:09:24,007 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/e637f6d3-1816-4508-99b9-2a3048e217f9/extract "HTTP/1.1 200 OK"
2025-08-04 02:09:36,143 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 02:09:36,169 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:09:40,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:09:40,320 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:09:42,339 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Observe and identify product elements specific to ...
2025-08-04 02:09:43,871 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/e637f6d3-1816-4508-99b9-2a3048e217f9/observe "HTTP/1.1 200 OK"
2025-08-04 02:09:45,811 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-04 02:09:45,844 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:09:49,555 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:09:49,557 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:09:51,573 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all products in the 'Fruit, Veg & Flowers ...
2025-08-04 02:09:53,092 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/e637f6d3-1816-4508-99b9-2a3048e217f9/extract "HTTP/1.1 200 OK"
2025-08-04 02:09:56,148 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 02:09:56,179 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:00,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:00,945 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:00,959 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:06,501 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:06,506 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:07,808 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:10:07,836 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:12,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:12,372 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:13,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:10:14,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:10:14,529 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:17,996 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:18,000 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:18,162 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:23,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:23,528 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:23,693 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:28,063 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:28,065 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:28,136 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:32,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:32,621 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:32,649 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:37,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:37,044 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:37,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:10:37,790 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:10:43,442 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:10:43,454 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:10:44,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:10:44,738 - ai_logger_scraper_20250804_020748 - INFO - TASK [CrewAI_System] crew_category_20250804_020748: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 02:10:44,743 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 02:10:44,755 - ai_logger_scraper_20250804_020748 - INFO - Session summary saved: logs\scraper_20250804_020748\session_summary.json
2025-08-04 02:10:44,756 - ai_logger_scraper_20250804_020748 - INFO - AI Logger session closed: scraper_20250804_020748
2025-08-04 02:17:29,947 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 02:17:35,479 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 02:17:41,522 - ai_logger_scraper_20250804_021741 - INFO - AI Logger initialized for session: scraper_20250804_021741
2025-08-04 02:17:41,532 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 02:17:41,533 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 02:17:41,554 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 02:17:41,556 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 02:17:41,557 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:17:41,557 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 02:17:41,558 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 02:17:41,560 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:17:41,582 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 02:17:41,623 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 02:17:41,641 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:17:42,602 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:17:42,981 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 02:17:43,504 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:17:43,560 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 02:17:43,566 - ai_logger_scraper_20250804_021741 - INFO - TASK [CrewAI_System] crew_category_20250804_021741: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 02:17:45,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:17:46,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:17:46,827 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:18:09,143 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-04 02:18:09,151 - ai_logger_scraper_20250804_021741 - INFO - Session summary saved: logs\scraper_20250804_021741\session_summary.json
2025-08-04 02:18:09,152 - ai_logger_scraper_20250804_021741 - INFO - AI Logger session closed: scraper_20250804_021741
2025-08-04 02:21:46,679 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 02:21:52,018 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 02:21:58,120 - ai_logger_scraper_20250804_022158 - INFO - AI Logger initialized for session: scraper_20250804_022158
2025-08-04 02:21:58,128 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 02:21:58,128 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 02:21:58,129 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 02:21:58,130 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 02:21:58,131 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:21:58,132 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 02:21:58,133 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 02:21:58,134 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:21:58,136 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 02:21:58,137 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 02:21:58,137 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:21:59,093 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:21:59,447 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 02:21:59,982 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:22:00,046 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 02:22:00,051 - ai_logger_scraper_20250804_022158 - INFO - TASK [CrewAI_System] crew_category_20250804_022158: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 02:22:04,460 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:22:06,748 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:22:07,019 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:22:34,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:22:34,801 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:22:49,898 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:22:51,208 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:23:03,621 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:23:03,631 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:23:05,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:06,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:06,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:07,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:08,265 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:08,953 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:10,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:11,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:12,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:12,683 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:23:12,728 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:23:21,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:23:21,354 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:23:24,505 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 02:23:26,020 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/b896344c-e8e2-4570-976a-b86f216b35c4 "HTTP/1.1 200 OK"
2025-08-04 02:23:33,096 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/b896344c-e8e2-4570-976a-b86f216b35c4/navigate "HTTP/1.1 200 OK"
2025-08-04 02:23:42,137 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:23:46,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:23:46,483 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:23:53,505 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product names, descriptions, prices, image...
2025-08-04 02:24:17,047 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/b896344c-e8e2-4570-976a-b86f216b35c4/extract "HTTP/1.1 200 OK"
2025-08-04 02:24:19,579 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 02:24:19,612 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:24:23,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:24:23,678 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:24:27,702 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Observe the page structure to identify product ele...
2025-08-04 02:24:29,276 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/b896344c-e8e2-4570-976a-b86f216b35c4/observe "HTTP/1.1 200 OK"
2025-08-04 02:24:32,423 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-04 02:24:32,458 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:24:36,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:24:36,336 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:24:40,361 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Observe the page structure to identify product ele...
2025-08-04 02:24:41,934 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/b896344c-e8e2-4570-976a-b86f216b35c4/observe "HTTP/1.1 200 OK"
2025-08-04 02:25:05,742 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-04 02:25:05,773 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:25:10,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:25:10,431 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:25:10,456 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:25:19,140 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:25:19,147 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:25:21,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:25:21,301 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:25:27,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:25:27,341 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:25:28,491 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:25:29,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:25:29,745 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:25:35,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:25:35,798 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:25:36,049 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:25:42,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:25:42,136 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:25:42,226 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:25:48,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:25:48,289 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:25:48,363 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:25:55,749 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:25:55,755 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:25:55,781 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:26:03,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:26:03,065 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:26:04,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:26:05,011 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:26:12,764 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:26:12,774 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:26:13,805 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:26:15,439 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:26:16,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:26:17,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:26:20,901 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/b896344c-e8e2-4570-976a-b86f216b35c4/end "HTTP/1.1 200 OK"
2025-08-04 02:49:11,630 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 02:49:17,024 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 02:49:22,892 - ai_logger_scraper_20250804_024922 - INFO - AI Logger initialized for session: scraper_20250804_024922
2025-08-04 02:49:22,901 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 02:49:22,902 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 02:49:22,903 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 02:49:22,903 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 02:49:22,904 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:49:22,905 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 02:49:22,905 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 02:49:22,906 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:49:22,907 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 02:49:22,908 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 02:49:22,908 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:49:23,764 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:49:24,095 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 02:49:24,597 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:49:24,639 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 02:49:24,645 - ai_logger_scraper_20250804_024922 - INFO - TASK [CrewAI_System] crew_category_20250804_024922: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 02:49:25,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:49:26,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:49:26,806 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:55:32,274 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 02:55:36,745 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 02:56:39,362 - ai_logger_scraper_20250804_025639 - INFO - AI Logger initialized for session: scraper_20250804_025639
2025-08-04 02:56:39,366 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 02:56:39,367 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 02:56:39,368 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 02:56:39,369 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 02:56:39,370 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:56:39,371 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 02:56:39,372 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 02:56:39,373 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:56:39,375 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 02:56:39,376 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 02:56:39,377 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 02:56:40,254 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:56:40,576 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 02:56:41,098 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 02:56:41,145 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 02:56:41,150 - ai_logger_scraper_20250804_025639 - INFO - TASK [CrewAI_System] crew_category_20250804_025639: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 02:56:42,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:56:44,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:56:44,804 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:56:50,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:56:50,924 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:56:53,577 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 02:56:54,961 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/1123adda-b937-4874-9073-1ea445a7489d "HTTP/1.1 200 OK"
2025-08-04 02:56:58,194 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/1123adda-b937-4874-9073-1ea445a7489d/navigate "HTTP/1.1 200 OK"
2025-08-04 02:57:06,792 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:57:09,679 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:57:09,688 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:57:14,700 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data using StandardizedProduct sch...
2025-08-04 02:57:16,621 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/1123adda-b937-4874-9073-1ea445a7489d/extract "HTTP/1.1 200 OK"
2025-08-04 02:58:12,643 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 02:58:12,667 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:58:20,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:58:20,699 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:58:20,754 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:58:24,975 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:58:25,037 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:58:25,717 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:58:27,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:58:27,056 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:58:36,061 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:58:36,063 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:58:36,101 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:58:44,846 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:58:44,848 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:58:44,891 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:58:58,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:58:58,294 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:58:59,568 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:00,621 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:59:06,944 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:59:06,947 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:59:07,633 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:08,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:08,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:08,764 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:08,842 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:59:16,423 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:59:16,425 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:59:17,869 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:17,906 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:59:22,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:59:22,923 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:59:23,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:24,135 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:25,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:26,811 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 02:59:26,870 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:59:31,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:59:31,700 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:59:34,491 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-04 02:59:35,911 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/99fbad7b-4190-4862-a81e-1ef67864edf2 "HTTP/1.1 200 OK"
2025-08-04 02:59:41,620 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/99fbad7b-4190-4862-a81e-1ef67864edf2/navigate "HTTP/1.1 200 OK"
2025-08-04 02:59:49,521 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 02:59:53,123 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 02:59:53,127 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 02:59:55,187 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Observe the product data structure on the Asda Fru...
2025-08-04 02:59:57,287 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/99fbad7b-4190-4862-a81e-1ef67864edf2/observe "HTTP/1.1 200 OK"
2025-08-04 02:59:59,113 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-04 02:59:59,156 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:00:01,744 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:00:01,747 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:00:01,796 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:00:04,819 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:00:04,824 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:00:06,847 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product data including name, description, ...
2025-08-04 03:00:08,407 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/99fbad7b-4190-4862-a81e-1ef67864edf2/extract "HTTP/1.1 200 OK"
2025-08-04 03:00:11,904 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-04 03:00:11,938 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:00:25,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:00:25,112 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:00:25,143 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:00:38,933 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:00:38,935 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:00:40,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:00:40,271 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:00:44,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:00:44,020 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:00:46,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:00:50,481 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:00:51,548 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:00:52,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:00:52,462 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:00:56,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:00:56,872 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:00:57,019 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:01:01,603 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:01:01,605 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:01:01,666 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:01:07,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:01:07,661 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:01:07,755 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:01:10,720 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:01:10,724 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:01:10,751 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:01:13,783 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:01:13,789 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:01:14,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:01:14,348 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-04 03:01:24,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 03:01:24,074 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 03:01:25,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:01:26,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:01:27,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:01:28,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:01:28,680 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:01:29,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 03:01:29,325 - ai_logger_scraper_20250804_025639 - INFO - TASK [CrewAI_System] crew_category_20250804_025639: CREW_COMPLETED - Crew execution completed successfully
2025-08-04 03:01:29,346 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-04 03:01:31,345 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/1123adda-b937-4874-9073-1ea445a7489d/end "HTTP/1.1 200 OK"
2025-08-04 03:01:31,449 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-04 03:01:31,450 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-04 03:01:31,474 - ai_logger_scraper_20250804_025639 - INFO - Session summary saved: logs\scraper_20250804_025639\session_summary.json
2025-08-04 03:01:31,475 - ai_logger_scraper_20250804_025639 - INFO - AI Logger session closed: scraper_20250804_025639
2025-08-04 03:11:13,707 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-04 03:11:18,557 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-04 03:11:24,499 - ai_logger_scraper_20250804_031124 - INFO - AI Logger initialized for session: scraper_20250804_031124
2025-08-04 03:11:24,506 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195303 - asda/Rollback
2025-08-04 03:11:24,507 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_195950 - asda/Rollback
2025-08-04 03:11:24,508 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200053 - asda/Rollback
2025-08-04 03:11:24,509 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback
2025-08-04 03:11:24,510 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200355 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 03:11:24,511 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_200800 - asda/Rollback
2025-08-04 03:11:24,512 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback
2025-08-04 03:11:24,513 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_201246 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 03:11:24,515 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_202853 - asda/Rollback
2025-08-04 03:11:24,516 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback
2025-08-04 03:11:24,517 - ecommerce_scraper.state.state_manager - INFO - Loaded session state: scraping_20250803_203214 - asda/Rollback > Laundry, Household & Toiletries
2025-08-04 03:11:25,652 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 03:11:26,090 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-04 03:11:26,703 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-04 03:11:26,764 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-04 03:11:26,770 - ai_logger_scraper_20250804_031124 - INFO - TASK [CrewAI_System] crew_category_20250804_031124: CREW_STARTED - Crew execution started with 3 agents and 3 tasks
2025-08-04 03:11:27,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
